<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.extend.mapper.ExtendTargetProgressMapper">

    <resultMap id="BaseResultMap" type="com.yooa.extend.api.domain.ExtendTargetProgress">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="date" column="date" jdbcType="DATE"/>
        <result property="agentAdminId" column="agent_admin_id" jdbcType="INTEGER"/>
        <result property="qualityUsers" column="quality_users" jdbcType="INTEGER"/>
        <result property="receive" column="receive" jdbcType="INTEGER"/>
        <result property="receiveVip" column="receive_vip" jdbcType="INTEGER"/>
        <result property="fortyFiveDays" column="forty_five_days" jdbcType="INTEGER"/>
        <result property="realProfit" column="real_profit" jdbcType="DECIMAL"/>
        <result property="chargeUser" column="charge_user" jdbcType="INTEGER"/>
    </resultMap>

    <!--  mybatis原生  -->

    <resultMap id="BaseResultMap2" type="com.yooa.extend.api.domain.ExtendTargetProgress">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="date" column="dateYMD" jdbcType="DATE"/>
        <result property="agentAdminId" column="agent_admin_id" jdbcType="INTEGER"/>
        <result property="qualityUsers" column="quality_users" jdbcType="INTEGER"/>
        <result property="receive" column="receive" jdbcType="INTEGER"/>
        <result property="receiveVip" column="receive_vip" jdbcType="INTEGER"/>
        <result property="fortyFiveDays" column="forty_five_days" jdbcType="INTEGER"/>
        <result property="realProfit" column="real_profit" jdbcType="DECIMAL"/>
        <result property="chargeUser" column="charge_user" jdbcType="INTEGER"/>
    </resultMap>


    <select id="index" resultType="com.yooa.extend.api.domain.vo.IndexVo">
        SELECT
        IFNULL( SUM( ${dto.fieldsName} ), 0 ) AS value,
        COUNT(DISTINCT u.user_id) AS peopleNumber
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
        LEFT JOIN (
            SELECT * FROM extend_target_progress AS e
            <where>
                <if test="dto.beginTime != null">
                    e.date >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND e.date &lt;= #{dto.endTime}
                </if>
            </where>
        ) AS e ON e.agent_admin_id = up.pd_user_id
    </select>

    <select id="indexAll" resultType="com.yooa.extend.api.domain.vo.IndexAllVo">
        SELECT
        IFNULL(SUM(quality_users), 0) AS quality_users,
        IFNULL(SUM(receive), 0) AS receive,
        IFNULL(SUM(receive_vip), 0) AS receive_vip,
        IFNULL(SUM(forty_five_days), 0) AS forty_five_days,
        IFNULL(SUM(real_profit), 0) AS real_profit,
        IFNULL(SUM(charge_user), 0) AS charge_user,
        COUNT(DISTINCT u.user_id) AS peopleNumber
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
        LEFT JOIN (
            SELECT * FROM extend_target_progress AS e
            <where>
                <if test="dto.beginTime != null">
                    e.date >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND e.date &lt;= #{dto.endTime}
                </if>
            </where>
        ) AS e ON e.agent_admin_id = up.pd_user_id
    </select>

    <select id="indexDeptGroup" resultType="com.yooa.extend.api.domain.vo.IndexDeptGroupVo">
        SELECT
            d.dept_id,
            d.dept_name,
            IFNULL( SUM( ${dto.fieldsName} ), 0 ) AS value
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
        LEFT JOIN (
            SELECT * FROM extend_target_progress AS e
            <where>
                <if test="dto.beginTime != null">
                    e.date >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND e.date &lt;= #{dto.endTime}
                </if>
            </where>
        ) AS e ON e.agent_admin_id = up.pd_user_id
        GROUP BY d.dept_id
    </select>

    <select id="indexDeptGroupAll" resultType="com.yooa.extend.api.domain.vo.IndexDeptGroupAllVo">
        SELECT
            d.dept_id,
            d.dept_name,
            IFNULL(SUM(quality_users), 0) AS quality_users,
            IFNULL(SUM(receive), 0) AS receive,
            IFNULL(SUM(receive_vip), 0) AS receive_vip,
            IFNULL(SUM(forty_five_days), 0) AS forty_five_days,
            IFNULL(SUM(real_profit), 0) AS real_profit,
            IFNULL(SUM(charge_user), 0) AS charge_user,
            ROUND(IFNULL(IFNULL(SUM(receive), 0) / IFNULL(SUM(quality_users), 0), 0), 2) AS receive_rate,
            ROUND(IFNULL(IFNULL(SUM(receive_vip), 0) / IFNULL(SUM(receive), 0), 0), 2) AS receiveVip_rate
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
        LEFT JOIN (
            SELECT * FROM extend_target_progress AS e
            <where>
                <if test="dto.beginTime != null">
                    e.date >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND e.date &lt;= #{dto.endTime}
                </if>
            </where>
        ) AS e ON e.agent_admin_id = up.pd_user_id
        GROUP BY d.dept_id
    </select>

    <select id="indexUserGroup" resultType="com.yooa.extend.api.domain.vo.IndexUserGroupVo">
        SELECT
            IFNULL( SUM( ${dto.fieldsName} ), 0 ) AS value,
            u.user_id AS userId,
            u.nick_name AS nickName
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
        LEFT JOIN (
            SELECT * FROM extend_target_progress AS e
            <where>
                <if test="dto.beginTime != null">
                    e.date >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND e.date &lt;= #{dto.endTime}
                </if>
            </where>
        ) AS e ON e.agent_admin_id = up.pd_user_id
        <where>
            u.user_id IS NOT NULL
            <if test="dto.userIds != null and dto.userIds.size() > 0">
                AND u.user_id IN
                <foreach collection="dto.userIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY u.user_id
    </select>

    <select id="indexUserAll" resultType="com.yooa.extend.api.domain.vo.IndexUserGroupAllVo">
        SELECT
            IFNULL(SUM(quality_users), 0) AS quality_users,
            IFNULL(SUM(receive), 0) AS receive,
            IFNULL(SUM(receive_vip), 0) AS receive_vip,
            IFNULL(SUM(forty_five_days), 0) AS forty_five_days,
            IFNULL(SUM(real_profit), 0) AS real_profit,
            IFNULL(SUM(charge_user), 0) AS charge_user,
            u.user_id AS userId,
            u.nick_name AS nickName
        FROM  yooa_system.sys_user AS u
        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
        LEFT JOIN (
            SELECT * FROM extend_target_progress AS e
            <where>
                <if test="dto.beginTime != null">
                    e.date >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND e.date &lt;= #{dto.endTime}
                </if>
            </where>
        ) AS e ON e.agent_admin_id = up.pd_user_id
        <where>
            <if test="dto.userIds != null and dto.userIds.size() > 0">
                u.user_id IN
                <foreach collection="dto.userIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="indexTimeGroup" resultType="com.yooa.extend.api.domain.vo.IndexTimeGroupVo">
        SELECT
            IFNULL( SUM( ${dto.fieldsName} ), 0 ) AS value
            <choose>
                <when test="dto.expType != null and dto.expType == 0">
                    ,CONCAT(DATE_FORMAT(e.date, '%Y-%m'), '-01') AS date
                </when>
                <otherwise>
                    ,e.date AS date
                </otherwise>
            </choose>
        FROM (
            <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
        LEFT JOIN (
            SELECT * FROM extend_target_progress AS e
            <where>
                <if test="dto.beginTime != null">
                    e.date >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND e.date &lt;= #{dto.endTime}
                </if>
            </where>
        ) AS e ON e.agent_admin_id = up.pd_user_id
        WHERE e.date IS NOT NULL
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                GROUP BY DATE_FORMAT(e.date, '%Y-%m')
            </when>
            <otherwise>
                GROUP BY e.date
            </otherwise>
        </choose>
    </select>

    <select id="indexTimeGroupAll" resultType="com.yooa.extend.api.domain.vo.IndexTimeGroupAllVo">
        SELECT
            IFNULL(SUM(quality_users), 0) AS quality_users,
            IFNULL(SUM(receive), 0) AS receive,
            IFNULL(SUM(receive_vip), 0) AS receive_vip,
            IFNULL(SUM(forty_five_days), 0) AS forty_five_days,
            IFNULL(SUM(real_profit), 0) AS real_profit,
            IFNULL(SUM(charge_user), 0) AS charge_user,
            ROUND(IFNULL(IFNULL(SUM(receive), 0) / IFNULL(SUM(quality_users), 0), 0), 2) AS receive_rate,
            ROUND(IFNULL(IFNULL(SUM(receive_vip), 0) / IFNULL(SUM(receive), 0), 0), 2) AS receiveVip_rate
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                ,CONCAT(DATE_FORMAT(e.date, '%Y-%m'), '-01') AS date
            </when>
            <otherwise>
                ,e.date AS date
            </otherwise>
        </choose>
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
        LEFT JOIN (
            SELECT * FROM extend_target_progress AS e
            <where>
                <if test="dto.beginTime != null">
                    e.date >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND e.date &lt;= #{dto.endTime}
                </if>
            </where>
        ) AS e ON e.agent_admin_id = up.pd_user_id
        <where>
            e.date IS NOT NULL
            <if test="dto.userIds != null and dto.userIds.size() > 0">
                AND u.user_id IN
                <foreach collection="dto.userIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <choose>
            <when test="dto.expType != null and dto.expType == 0">
                GROUP BY DATE_FORMAT(e.date, '%Y-%m')
            </when>
            <otherwise>
                GROUP BY e.date
            </otherwise>
        </choose>
    </select>

    <select id="selPeopleRanking" resultType="com.yooa.extend.api.domain.vo.IndexUserGroupAllVo">
        SELECT u.user_id,
               IFNULL(SUM(quality_users), 0)                                                AS quality_users,
               IFNULL(SUM(receive), 0)                                                      AS receive,
               IFNULL(SUM(receive_vip), 0)                                                  AS receive_vip,
               IFNULL(SUM(forty_five_days), 0)                                              AS forty_five_days,
               IFNULL(SUM(real_profit), 0)                                                  AS real_profit,
               IFNULL(SUM(charge_user), 0)                                                  AS charge_user,
               ROUND(IFNULL(IFNULL(SUM(receive), 0) / IFNULL(SUM(quality_users), 0), 0), 2) AS receive_rate,
               ROUND(IFNULL(IFNULL(SUM(receive_vip), 0) / IFNULL(SUM(receive), 0), 0), 2)   AS receiveVip_rate
        FROM yooa_system.sys_user u
                 INNER JOIN yooa_system.sys_user_pd up ON u.user_id = up.user_id
                 LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                 LEFT JOIN (
                    SELECT * FROM extend_target_progress tp
                    WHERE tp.date >= #{dto.beginTime}
                    AND tp.date &lt;= #{dto.endTime}
                 ) tp ON up.pd_user_id = tp.agent_admin_id
        WHERE u.status = 0
          AND d.dept_type = #{dto.selType}
        GROUP BY u.user_id
        ORDER BY u.user_id DESC
    </select>

    <insert id="insertAll">
        insert into extend_target_progress(`date`,`agent_admin_id`,`quality_users`,`receive`,
        `receive_vip`,`forty_five_days`,`real_profit`,`charge_user`)
        values
        <foreach collection='list' item='t' separator=','>
            (#{t.date}, #{t.agentAdminId}, #{t.qualityUsers}, #{t.receive},
            #{t.receiveVip},#{t.fortyFiveDays},#{t.realProfit},#{t.chargeUser})
        </foreach>
    </insert>

    <update id="updateAll" parameterType="java.util.List">
        <foreach collection="list" item="extend" index="index" separator=";" open="" close="">
            update extend_target_progress
            <trim prefix="SET" suffixOverrides=",">
                <if test="extend.qualityUsers != null">`quality_users` = #{extend.qualityUsers},</if>
                <if test="extend.receive != null">`receive` = #{extend.receive},</if>
                <if test="extend.receiveVip != null">`receive_vip` = #{extend.receiveVip},</if>
                <if test="extend.fortyFiveDays != null">`forty_five_days` = #{extend.fortyFiveDays},</if>
                <if test="extend.realProfit != null">`real_profit` = #{extend.realProfit},</if>
                <if test="extend.chargeUser != null">`charge_user` = #{extend.chargeUser},</if>
            </trim>
            WHERE agent_admin_id = #{extend.agentAdminId} AND `date` = #{extend.date}
        </foreach>
    </update>

    <select id="detailedUserLimits" resultType="com.yooa.extend.api.domain.vo.DetailedVo">
        SELECT
            u.user_id       AS id,
            u.nick_name     AS name,
            u.dept_id,
            d.leader,
            CONCAT(d.ancestors_names, '-', d.dept_name) AS ancestors_names,
            CASE WHEN u.user_id = d.leader
                THEN
                    (
                        SELECT u1.nick_name
                        FROM yooa_system.sys_dept AS d1
                        LEFT JOIN yooa_system.sys_user AS u1 ON d1.leader = u1.user_id
                        WHERE d1.dept_id = d.parent_id
                    )
                ELSE
                    (
                        SELECT u1.nick_name
                        FROM yooa_system.sys_user AS u1
                        WHERE u1.user_id = d.leader
                    )
            END leader_name,
            GROUP_CONCAT(pd_user_id) AS pdUserIds
        FROM yooa_system.sys_user AS u
        LEFT JOIN yooa_system.sys_dept AS d ON u.dept_id = d.dept_id
        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
        <if test="dto.status != null" >
            RIGHT JOIN (
                SELECT * FROM yooa_system.sys_roster
                WHERE
                    employee_status = #{dto.status}
            ) AS r ON u.user_id = r.oa_user_id
        </if>
        <where>
                up.pd_user_id IS NOT NULL
            <if test="dto.name != null and dto.name != ''">
                AND u.nick_name like concat('%', #{dto.name}, '%')
            </if>
            <if test="dto.deptId != null and dto.deptId != 0">
                AND FIND_IN_SET(#{dto.deptId},CONCAT(d.ancestors ,',', d.dept_id))
            </if>
            <if test="dto.selType != null and dto.selType != 0">
                AND d.dept_type = #{dto.selType}
            </if>
            <if test="dto.hierarchy != null and dto.hierarchy != 0">
                AND d.hierarchy = #{dto.hierarchy}
            </if>
            <if test="dto.parentId != null and dto.parentId != 0">
                AND d.parent_id = #{dto.parentId}
            </if>
            ${dto.params.dataScope}
        </where>
        GROUP by u.user_id
    </select>

    <select id="ExtendDetailedDeptLimits" resultType="com.yooa.extend.api.domain.vo.DetailedVo">
        SELECT
            d.dept_id       AS id,
            d.dept_name     AS name,
            d.leader,
            d.ancestors_names,
            (
                SELECT u1.nick_name
                FROM yooa_system.sys_user AS u1
                WHERE u1.user_id = d.leader
            ) AS leader_name
        FROM (
            SELECT
                d.dept_id,
                d.dept_name,
                GROUP_CONCAT(d1.dept_id) AS juniorIds,
                CONCAT(d.ancestors_names, '-', d.dept_name) AS ancestors_names,
                d.leader
            FROM yooa_system.sys_dept AS d
            LEFT JOIN yooa_system.sys_dept AS d1 ON FIND_IN_SET(d.dept_id,d1.ancestors) OR d.dept_id = d1.dept_id
            <where>
                <if test="dto.deptId != null and dto.deptId != 0">
                    FIND_IN_SET(#{dto.deptId},CONCAT(d.ancestors ,',', d.dept_id))
                </if>
                <if test="dto.selType != null and dto.selType != 0">
                    AND d.dept_type = #{dto.selType}
                </if>
                <if test="dto.hierarchy != null and dto.hierarchy != 0">
                    AND d.hierarchy = #{dto.hierarchy}
                </if>
            </where>
            GROUP BY d.dept_id
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        <if test="dto.status != null" >
            RIGHT JOIN (
                SELECT * FROM yooa_system.sys_roster
                WHERE
                    employee_status = #{dto.status}
            ) AS r ON u.user_id = r.oa_user_id
        </if>
        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
        <where>
            up.user_id IS NOT NULL
            ${dto.params.dataScope}
        </where>
        GROUP BY d.dept_id
    </select>

    <select id="ExtendDetailedUserData" resultType="com.yooa.extend.api.domain.vo.DetailedVo">
        SELECT
            IFNULL( e.fans2h, 0 )                           AS fans2h,
            IFNULL( e.fans5h, 0 )                           AS fans5h,
            IFNULL( e.fans5k, 0 )                           AS fans5k,
            IFNULL( e.fans5kNew, 0 )                        AS fans5kNew,
            IFNULL( e.fans5w, 0 )                           AS fans5w,
            IFNULL( e.firstCharge, 0 )                      AS firstCharge,
            o.order_info                                    AS order_info,
            IFNULL( r.receive , 0 )                         AS receive,
            IFNULL( v.receiveVip , 0 )                      AS receiveVip,
            IFNULL( f.friend_id, 0 )                        AS friend,
            IFNULL( c.register, 0 )                         AS register,
            IFNULL( q.qualityUsers, 0 )                     AS qualityUsers,
            u.user_id                                       AS id
        FROM (
            SELECT
                u.user_id,
                GROUP_CONCAT(up.pd_user_id) AS pd_user_ids
            FROM yooa_system.sys_user AS u
            LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
            WHERE
                FIND_IN_SET(u.user_id,#{userIds})
            GROUP BY u.user_id
        ) AS u
        <!--关联粉丝登记-->
        LEFT JOIN (
            SELECT
                SUM(CASE WHEN e.fans_type = 0 THEN 1 ELSE 0 END) AS fans2h,
                SUM(CASE WHEN e.fans_type = 1 THEN 1 ELSE 0 END) AS fans5h,
                SUM(CASE WHEN e.fans_type = 2 THEN 1 ELSE 0 END) AS fans5k,
                SUM(CASE WHEN e.fans_type = 2 AND e.status = 0 THEN 1 ELSE 0 END) AS fans5kNew,
                SUM(CASE WHEN e.fans_type = 3 THEN 1 ELSE 0 END) AS fans5w,
                SUM(CASE WHEN e.fans_type = 5 THEN 1 ELSE 0 END) AS firstCharge,
                <choose>
                    <when test="dto.selType == 7">
                        e.serve_id                                            AS e_user_id
                    </when>
                    <otherwise>
                        e.extend_id                                           AS e_user_id
                    </otherwise>
                </choose>
            FROM
            <choose>
                <when test="dto.selType == 7">
                    vip_vermicelli AS e
                </when>
                <otherwise>
                    extend_vermicelli AS e
                </otherwise>
            </choose>
            <if test="dto.fansType != null">
                RIGHT JOIN yooa_crm.crm_friend AS f ON e.friend_id = f.friend_id AND f.fans_type = #{dto.fansType}
            </if>
            <where>
                <if test="dto.beginTime != null">
                    e.record_date >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND e.record_date &lt;= #{dto.endTime}
                </if>
                <choose>
                    <when test="dto.selType == 7">
                        AND FIND_IN_SET(e.serve_id,#{userIds})
                    </when>
                    <otherwise>
                        AND FIND_IN_SET(e.extend_id,#{userIds})
                    </otherwise>
                </choose>
            </where>
            GROUP BY e_user_id
        ) AS e ON u.user_id = e.e_user_id
        <!--关联好友表-->
        LEFT JOIN (
            SELECT
                COUNT( f.friend_id ) AS friend_id,
                f.extend_id
            FROM yooa_crm.crm_friend AS f
            <where>
                FIND_IN_SET(f.extend_id,#{userIds})
                <if test="dto.beginTime != null">
                    AND f.create_time >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND f.create_time &lt;= #{dto.endTime}
                </if>
                <if test="dto.fansType != null">
                    AND f.fans_type = #{dto.fansType}
                </if>
            </where>
            GROUP BY f.extend_id
        ) AS f ON u.user_id = f.extend_id
        <!--关联注册-->
        LEFT JOIN (
            SELECT
                up.user_id,
                COUNT(DISTINCT cf.friend_id) AS register
            FROM (
                SELECT
                    py_extend_id,
                    friend_id,
                    begin_time
                FROM yooa_crm.crm_customer_friend
                GROUP BY py_extend_id,friend_id
            ) AS cf
            LEFT JOIN yooa_system.sys_user_pd AS up ON cf.py_extend_id = up.pd_user_id
            <if test="dto.fansType != null">
                RIGHT JOIN yooa_crm.crm_friend AS f ON cf.friend_id = f.friend_id AND f.fans_type = #{dto.fansType}
            </if>
            <where>
                <if test="dto.beginTime != null">
                    AND cf.begin_time >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND cf.begin_time &lt;= #{dto.endTime}
                </if>
            </where>
            GROUP BY up.user_id
        ) AS c ON u.user_id = c.user_id
        <!--关联优质-->
        LEFT JOIN (
            SELECT
                COUNT(DISTINCT cf.friend_id) AS qualityUsers,
                up.user_id
            FROM (
                SELECT
                    py_extend_id,
                    friend_id,
                    quality_time,
                    begin_time
                FROM yooa_crm.crm_customer_friend
                GROUP BY py_extend_id,friend_id
            ) AS cf
            LEFT JOIN yooa_system.sys_user_pd AS up ON cf.py_extend_id = up.pd_user_id
            <if test="dto.fansType != null">
                RIGHT JOIN yooa_crm.crm_friend AS f ON cf.friend_id = f.friend_id AND f.fans_type = #{dto.fansType}
            </if>
            WHERE
                cf.quality_time IS NOT NULL
                <if test="dto.beginTime != null">
                    AND cf.begin_time >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND cf.begin_time &lt;= #{dto.endTime}
                </if>
                <if test="dto.userId != null and dto.userId != 0">
                    AND up.user_id = #{dto.userId}
                </if>
            GROUP BY up.user_id
        ) AS q ON u.user_id = q.user_id
        <!--关联一交表-->
        LEFT JOIN (
            SELECT
                COUNT( cja.friend_id) AS receive,
                cja.user_id
            FROM (
                SELECT
                    cja.receive_time,
                    cja.id,
                    cf.customer_id,
                    cf.friend_id,
                    cf.user_id
                FROM (
                    SELECT *
                    FROM yooa_crm.crm_customer_join_anchor
                    WHERE
                        status = '1'
                        AND FIND_IN_SET(extend_id, #{pdUserIds})
                ) AS cja
                LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cja.extend_id
                JOIN (
                    SELECT
                        cf.customer_id,
                        up.user_id,
                        cf.begin_time,
                        cf.end_time,
                        cf.friend_id
                    FROM yooa_crm.crm_customer_friend cf
                    LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cf.py_extend_id
                    WHERE
                        FIND_IN_SET(up.user_id,#{userIds})
                ) cf ON cja.customer_id = cf.customer_id AND up.user_id = cf.user_id AND cf.begin_time &lt;= cja.receive_time AND (cf.end_time IS NULL OR cf.end_time > cja.receive_time)
                <if test="dto.fansType != null">
                    RIGHT JOIN yooa_crm.crm_friend AS f ON cf.friend_id = f.friend_id AND f.fans_type = #{dto.fansType}
                </if>
                GROUP BY cf.user_id,cf.friend_id
            )   cja
            <where>
                <if test="dto.beginTime != null">
                    AND cja.receive_time >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND cja.receive_time &lt;= #{dto.endTime}
                </if>
            </where>
            GROUP BY cja.user_id
        ) AS r ON u.user_id = r.user_id
        <!--关联二交表-->
        LEFT JOIN (
            SELECT
                COUNT( s.friend_id) AS receiveVip,
                s.user_id
            FROM (
                SELECT
                    s.receive_time,
                    s.id,
                    cf.customer_id,
                    cf.friend_id,
                    up.user_id
                FROM (
                    SELECT
                        id,
                        customer_id,
                        serve_id,
                        receive_time,
                        extend_id
                    FROM yooa_crm.crm_customer_join_serve
                    WHERE
                        status = '1'
                        AND FIND_IN_SET(
                            <choose>
                                <when test="dto.selType == 7">
                                    serve_id
                                </when>
                                <otherwise>
                                    extend_id
                                </otherwise>
                            </choose>
                            , #{pdUserIds})
                ) AS s
                LEFT JOIN yooa_system.sys_user_pd AS up ON
                <choose>
                    <when test="dto.selType == 7">
                        up.pd_user_id = s.serve_id
                    </when>
                    <otherwise>
                        up.pd_user_id = s.extend_id
                    </otherwise>
                </choose>
                JOIN (
                    SELECT
                        cf.customer_id,
                        cf.begin_time,
                        cf.py_extend_id,
                        cf.end_time,
                        cf.friend_id
                    FROM yooa_crm.crm_customer_friend cf
                ) cf ON s.customer_id = cf.customer_id AND s.extend_id = cf.py_extend_id AND cf.begin_time &lt;= s.receive_time AND (cf.end_time IS NULL OR cf.end_time > s.receive_time)
                <if test="dto.fansType != null">
                    RIGHT JOIN yooa_crm.crm_friend AS f ON cf.friend_id = f.friend_id AND f.fans_type = #{dto.fansType}
                </if>
                GROUP BY up.user_id,cf.friend_id
            )   s
            <where>
                <if test="dto.beginTime != null">
                    AND s.receive_time >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND s.receive_time &lt;= #{dto.endTime}
                </if>
            </where>
            GROUP BY s.user_id
        ) AS v ON u.user_id = v.user_id
        <!--订单-->
        LEFT JOIN (
            SELECT
                JSON_OBJECT('charge_user',IFNULL(count(DISTINCT o.customer_id),0),
                'order_money',IFNULL(SUM(o.order_money),0),
                'order_new_money',IFNULL(SUM(CASE WHEN DATE_ADD(c.update_time, INTERVAL 45 DAY) > o.order_time AND c.update_time &lt; order_time THEN o.order_money ELSE 0 END),0)) AS order_info,
                o.user_id
            FROM
            (
                SELECT
                    customer_id,
                    order_time,
                    order_money,
                    <choose>
                        <when test="dto.selType == 7">
                            py_serve_id        AS pd_user_id,
                            serve_id           AS user_id
                        </when>
                        <otherwise>
                            py_extend_id       AS pd_user_id,
                            extend_id          AS user_id
                        </otherwise>
                    </choose>
                FROM yooa_crm.crm_customer_order
                <where>
                    order_status = 1
                    <if test="dto.beginTime != null">
                        AND order_time >= #{dto.beginTime}
                    </if>
                    <if test="dto.endTime != null">
                        AND order_time &lt;= #{dto.endTime}
                    </if>
                    <choose>
                        <when test="dto.selType == 7">
                            AND FIND_IN_SET(serve_id,  #{userIds})
                        </when>
                        <otherwise>
                            AND FIND_IN_SET(extend_id, #{userIds})
                        </otherwise>
                    </choose>
                </where>
            ) AS o
            LEFT JOIN yooa_crm.crm_customer AS c ON c.customer_id = o.customer_id AND
            <choose>
                <when test="dto.selType == 7">
                    c.serve_id =  o.pd_user_id
                </when>
                <otherwise>
                    c.extend_id = o.pd_user_id
                </otherwise>
            </choose>
            <if test="dto.fansType != null">
                LEFT JOIN yooa_crm.crm_customer_friend AS cf ON c.customer_id = cf.customer_id
                RIGHT JOIN yooa_crm.crm_friend AS f ON cf.friend_id = f.friend_id AND f.fans_type = #{dto.fansType}
            </if>
            GROUP BY o.user_id
        ) AS o ON u.user_id = o.user_id
        GROUP by u.user_id
    </select>

    <select id="ExtendDetailedDeptData" resultType="com.yooa.extend.api.domain.vo.DetailedVo">
        SELECT
            <!--粉丝登记-->
            (
                SELECT
                    JSON_OBJECT('fans2h', IFNULL(SUM( CASE WHEN e.fans_type = 0 THEN 1 ELSE 0 END ),0),
                                'fans5h', IFNULL(SUM( CASE WHEN e.fans_type = 1 THEN 1 ELSE 0 END ),0),
                                'fans5k', IFNULL(SUM( CASE WHEN e.fans_type = 2 THEN 1 ELSE 0 END ),0),
                                'fans5kNew', IFNULL(SUM( CASE WHEN e.fans_type = 2 AND e.STATUS = 0 THEN 1 ELSE 0 END ),0),
                                'fans5w', IFNULL(SUM( CASE WHEN e.fans_type = 3 THEN 1 ELSE 0 END ),0),
                                'firstCharge', IFNULL(SUM( CASE WHEN e.fans_type = 5 THEN 1 ELSE 0 END ),0))
                FROM
                    <choose>
                        <when test="dto.selType == 7">
                            vip_vermicelli AS e
                        </when>
                        <otherwise>
                            extend_vermicelli AS e
                        </otherwise>
                    </choose>
                <if test="dto.fansType != null">
                    RIGHT JOIN yooa_crm.crm_friend AS f ON e.friend_id = f.friend_id AND f.fans_type = #{dto.fansType}
                </if>
                WHERE
                    <choose>
                        <when test="dto.selType == 7">
                            FIND_IN_SET(e.serve_dept_id,d.juniorIds)
                        </when>
                        <otherwise>
                            FIND_IN_SET(e.extend_dept_id,d.juniorIds)
                        </otherwise>
                    </choose>
                <if test="dto.beginTime != null">
                    AND e.record_date >= #{dto.beginTime}
                </if>
                <if test="dto.endTime != null">
                    AND e.record_date &lt;= #{dto.endTime}
                </if>
            )                                                             AS fans_info,
            <!--订单-->
            (
                SELECT
                    JSON_OBJECT('charge_user',IFNULL(count(DISTINCT o.customer_id),0),
                                'order_money',IFNULL(SUM(o.order_money),0),
                                'order_new_money',IFNULL(SUM(CASE WHEN DATE_ADD(c.update_time, INTERVAL 45 DAY) > o.order_time AND c.update_time &lt; order_time THEN o.order_money ELSE 0 END),0))
                FROM
                (
                    SELECT
                        customer_id,
                        order_time,
                        order_money,
                        py_extend_id,
                        py_serve_id,
                    <choose>
                        <when test="dto.selType == 7">
                            serve_dept_id        AS pd_dept_id
                        </when>
                        <otherwise>
                            extend_dept_id       AS pd_dept_id
                        </otherwise>
                    </choose>
                    FROM yooa_crm.crm_customer_order
                    <where>
                        order_status = 1
                        <if test="dto.beginTime != null">
                            AND order_time >= #{dto.beginTime}
                        </if>
                        <if test="dto.endTime != null">
                            AND order_time &lt;= #{dto.endTime}
                        </if>
                        <choose>
                            <when test="dto.selType == 7">
                                AND FIND_IN_SET(serve_dept_id,d.juniorIds)
                            </when>
                            <otherwise>
                                AND FIND_IN_SET(extend_dept_id,d.juniorIds)
                            </otherwise>
                        </choose>
                    </where>
                ) AS o
                LEFT JOIN yooa_crm.crm_customer AS c ON c.customer_id = o.customer_id AND
                <choose>
                    <when test="dto.selType == 7">
                        c.serve_id =  o.py_serve_id
                    </when>
                    <otherwise>
                        c.extend_id = o.py_extend_id
                    </otherwise>
                </choose>
                <if test="dto.fansType != null">
                    LEFT JOIN yooa_crm.crm_customer_friend AS cf ON o.customer_id = cf.customer_id
                    RIGHT JOIN yooa_crm.crm_friend AS f ON cf.friend_id = f.friend_id AND f.fans_type = #{dto.fansType}
                </if>
                WHERE FIND_IN_SET( o.pd_dept_id, d.juniorIds )
            )                                                             AS order_info,
            <!--好友-->
            IFNULL( (
                SELECT
                    COUNT(DISTINCT f.friend_id ) AS friend_id
                FROM yooa_crm.crm_friend AS f
                LEFT JOIN yooa_system.sys_user AS u on u.user_id = f.extend_id
                <where>
                    FIND_IN_SET(u.dept_id,d.juniorIds)
                    <if test="dto.beginTime != null">
                        AND f.create_time >= #{dto.beginTime}
                    </if>
                    <if test="dto.endTime != null">
                        AND f.create_time &lt;= #{dto.endTime}
                    </if>
                    <if test="dto.fansType != null">
                        AND f.fans_type = #{dto.fansType}
                    </if>
                </where>
            ), 0 )                                                        AS friend,
            <!--注册-->
            IFNULL( (
                SELECT
                    COUNT(DISTINCT cf.friend_id) AS register
                FROM (
                    SELECT
                        py_extend_id,
                        friend_id,
                        begin_time
                    FROM yooa_crm.crm_customer_friend
                    GROUP BY py_extend_id,friend_id
                ) AS cf
                LEFT JOIN yooa_system.sys_user_pd AS up ON cf.py_extend_id = up.pd_user_id
                LEFT JOIN yooa_system.sys_user AS u ON up.user_id = u.user_id
                <if test="dto.fansType != null">
                    RIGHT JOIN yooa_crm.crm_friend AS f ON cf.friend_id = f.friend_id AND f.fans_type = #{dto.fansType}
                </if>
                <where>
                    FIND_IN_SET(u.dept_id,d.juniorIds)
                    <if test="dto.beginTime != null">
                        AND cf.begin_time >= #{dto.beginTime}
                    </if>
                    <if test="dto.endTime != null">
                        AND cf.begin_time &lt;= #{dto.endTime}
                    </if>
                </where>
            ), 0 )                                                        AS register,
            <!--优质-->
            IFNULL( (
                SELECT
                    COUNT(DISTINCT cf.friend_id) AS qualityUsers
                FROM (
                    SELECT
                        py_extend_id,
                        friend_id,
                        begin_time
                    FROM yooa_crm.crm_customer_friend
                    WHERE quality_time IS NOT NULL
                    GROUP BY py_extend_id,friend_id
                ) AS cf
                LEFT JOIN yooa_system.sys_user_pd AS up ON cf.py_extend_id = up.pd_user_id
                LEFT JOIN yooa_system.sys_user AS u ON up.user_id = u.user_id
                <if test="dto.fansType != null">
                    RIGHT JOIN yooa_crm.crm_friend AS f ON cf.friend_id = f.friend_id AND f.fans_type = #{dto.fansType}
                </if>
                <where>
                    FIND_IN_SET(u.dept_id,d.juniorIds)
                    <if test="dto.beginTime != null">
                        AND cf.begin_time >= #{dto.beginTime}
                    </if>
                    <if test="dto.endTime != null">
                        AND cf.begin_time &lt;= #{dto.endTime}
                    </if>
                </where>
            ), 0 )                                                        AS qualityUsers,
            <!--一交-->
            IFNULL( (
                SELECT SUM(r.receive)
                FROM (
                    SELECT
                        COUNT( cja.friend_id) AS receive,
                        cja.dept_id
                    FROM (
                        SELECT
                            cja.receive_time,
                            cja.id,
                            cf.customer_id,
                            cf.friend_id,
                            u.dept_id,
                            cf.user_id
                        FROM (
                            SELECT *
                            FROM yooa_crm.crm_customer_join_anchor
                            WHERE
                                status = '1'
                        ) AS cja
                        LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cja.extend_id
                        LEFT JOIN yooa_system.sys_user AS u ON up.user_id = u.user_id
                        JOIN (
                            SELECT
                                cf.customer_id,
                                up.user_id,
                                cf.begin_time,
                                cf.end_time,
                                cf.friend_id
                            FROM yooa_crm.crm_customer_friend cf
                            LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cf.py_extend_id
                        ) cf ON cja.customer_id = cf.customer_id AND up.user_id = cf.user_id AND cf.begin_time &lt;= cja.receive_time AND (cf.end_time IS NULL OR cf.end_time > cja.receive_time)
                        <if test="dto.fansType != null">
                            RIGHT JOIN yooa_crm.crm_friend AS f ON cf.friend_id = f.friend_id AND f.fans_type = #{dto.fansType}
                        </if>
                        GROUP BY u.user_id,cf.friend_id
                    )   cja
                    <where>
                        <if test="dto.beginTime != null">
                            AND cja.receive_time >= #{dto.beginTime}
                        </if>
                        <if test="dto.endTime != null">
                            AND cja.receive_time &lt;= #{dto.endTime}
                        </if>
                    </where>
                    GROUP BY cja.dept_id
                )   r
                WHERE FIND_IN_SET(r.dept_id,d.juniorIds)
            ) , 0 )                                                       AS receive,
            <!--二交-->
            IFNULL( (
                SELECT SUM(s.receiveVip)
                FROM (
                    SELECT
                        COUNT( s.friend_id) AS receiveVip,
                        s.dept_id
                    FROM (
                        SELECT
                            s.receive_time,
                            s.id,
                            cf.customer_id,
                            cf.friend_id,
                            u.dept_id,
                            u.user_id
                        FROM (
                            SELECT
                                id,
                                customer_id,
                                serve_id,
                                receive_time,
                                extend_id
                            FROM yooa_crm.crm_customer_join_serve
                            WHERE
                                status = '1'
                        ) AS s
                        LEFT JOIN yooa_system.sys_user_pd AS up ON
                        <choose>
                            <when test="dto.selType == 7">
                                up.pd_user_id = s.serve_id
                            </when>
                            <otherwise>
                                up.pd_user_id = s.extend_id
                            </otherwise>
                        </choose>
                        LEFT JOIN yooa_system.sys_user AS u ON up.user_id = u.user_id
                        JOIN (
                            SELECT
                                cf.customer_id,
                                cf.begin_time,
                                cf.py_extend_id,
                                cf.end_time,
                                cf.friend_id
                            FROM yooa_crm.crm_customer_friend cf
                        ) cf ON s.customer_id = cf.customer_id AND s.extend_id = cf.py_extend_id AND cf.begin_time &lt;= s.receive_time AND (cf.end_time IS NULL OR cf.end_time > s.receive_time)
                        <if test="dto.fansType != null">
                            RIGHT JOIN yooa_crm.crm_friend AS f ON cf.friend_id = f.friend_id AND f.fans_type = #{dto.fansType}
                        </if>
                        GROUP BY u.user_id,cf.friend_id
                    )   s
                    <where>
                        <if test="dto.beginTime != null">
                            AND s.receive_time >= #{dto.beginTime}
                        </if>
                        <if test="dto.endTime != null">
                            AND s.receive_time &lt;= #{dto.endTime}
                        </if>
                    </where>
                    GROUP BY s.dept_id
                )   s
                WHERE FIND_IN_SET(s.dept_id,d.juniorIds)
            ), 0 )                                                        AS receiveVip,
            d.dept_id                                                     AS id
        FROM (
            SELECT
                d.dept_id,
                d.juniorIds                 AS juniorIds,
                GROUP_CONCAT(pd_user_id)    AS pd_user_ids
            FROM (
                SELECT
                    d.dept_id,
                    GROUP_CONCAT(d1.dept_id) AS juniorIds
                FROM yooa_system.sys_dept AS d
                LEFT JOIN yooa_system.sys_dept AS d1 ON FIND_IN_SET(d.dept_id,d1.ancestors) OR d.dept_id = d1.dept_id
                GROUP BY d.dept_id
            ) AS d
            LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
            LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
            WHERE
                FIND_IN_SET(d.dept_id,#{deptIds})
            GROUP BY d.dept_id
        ) AS d
        GROUP by d.dept_id
    </select>

    <select id="getPeopleNumber" resultType="java.lang.Long">
        SELECT
            COUNT(DISTINCT u.user_id) AS peopleNumber
        FROM (
        <include refid="getDeptJuniorIds"/>
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
    </select>

    <select id="getOperateUser" resultType="com.yooa.extend.api.domain.vo.DetailedVo">
        SELECT
        u.user_id       AS id,
        u.nick_name     AS name,
        u.dept_id,
        d.leader,
        CONCAT(d.ancestors_names, '-', d.dept_name) AS ancestors_names,
        CASE WHEN u.user_id = d.leader
        THEN
        (
        SELECT u1.nick_name
        FROM yooa_system.sys_dept AS d1
        LEFT JOIN yooa_system.sys_user AS u1 ON d1.leader = u1.user_id
        WHERE d1.dept_id = d.parent_id
        )
        ELSE
        (
        SELECT u1.nick_name
        FROM yooa_system.sys_user AS u1
        WHERE u1.user_id = d.leader
        )
        END leader_name,
        GROUP_CONCAT(pd_user_id) AS pdUserIds
        FROM yooa_system.sys_user AS u
        LEFT JOIN yooa_system.sys_dept AS d ON u.dept_id = d.dept_id
        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
        <if test="dto.status != null" >
            RIGHT JOIN (
            SELECT * FROM yooa_system.sys_roster
            WHERE
            employee_status = #{dto.status}
            ) AS r ON u.user_id = r.oa_user_id
        </if>
        <where>
            up.pd_user_id IS NOT NULL
            <if test="dto.name != null and dto.name != ''">
                AND u.nick_name like concat('%', #{dto.name}, '%')
            </if>
            <if test="dto.deptId != null and dto.deptId != 0">
                AND FIND_IN_SET(#{dto.deptId},CONCAT(d.ancestors ,',', d.dept_id))
            </if>
                AND d.dept_type = 2
            ${dto.params.dataScope}
        </where>
        GROUP by u.user_id
    </select>



    <select id="OperateDetailedUserData" resultType="com.yooa.extend.api.domain.vo.OperationalBusinessDataVo">
        SELECT
            IFNULL( e.fans2h, 0 ) AS fans2h,
            IFNULL( e.fans5k, 0 ) AS fans5h,
            IFNULL( e.fans5w, 0 ) AS fans5k,
            IFNULL( e.fans10w, 0 ) AS fans10k,
            IFNULL( e.fans5w, 0 ) AS fans5w,
            IFNULL( cja.firstChargeNum, 0 ) firstChargeNum,
            IFNULL( cja.handoverRewardNum, 0 ) handoverRewardNum,
            IFNULL( cja.handoverNum, 0 ) handoverNum,
            IFNULL( ai.sumBeginToShow, 0 ) beginToShowNum,
            IFNULL( ai.sumOffAirBroadcast, 0 ) offAirBroadcastNum,
            IFNULL( ai.sumCeaseBroadcasting, 0 ) discontinueBroadcastingNum,
            IFNULL( ai.toBeDiscontinued, 0 ) toBeDiscontinued,
            IFNULL( cr.rewardNum, 0 ) totalRewardNum,
            IFNULL( cr.rewardAmount, 0 ) rewardAmount,
        CASE
        WHEN IFNULL(cja.handoverNum, 0) = 0 THEN 0
        ELSE ROUND(cja.handoverRewardNum / cja.handoverNum * 100, 4)
        END AS handoverRewardRate,
        CASE
        WHEN IFNULL(cja.handoverNum, 0) = 0 THEN 0
        ELSE ROUND(cja.firstChargeNum / cja.handoverNum * 100, 4)
        END AS handoverFirstChargeRate,
            u.user_id id,
            u.nick_name operateName
        FROM
            (
                SELECT
                    u.user_id,
                    u.nick_name,
                    GROUP_CONCAT( up.pd_user_id ) AS pd_user_ids
                FROM
                    yooa_system.sys_user AS u
                        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
                WHERE
                    FIND_IN_SET(
                            u.user_id,
                            #{userIds})
                GROUP BY
                    u.user_id
            ) AS u
                LEFT JOIN (
                SELECT
                    SUM( CASE WHEN e.fans_type = 1 THEN 1 ELSE 0 END ) AS fans2h,
                    SUM( CASE WHEN e.fans_type = 3 THEN 1 ELSE 0 END ) AS fans5k,
                    SUM( CASE WHEN e.fans_type = 4 THEN 1 ELSE 0 END ) AS fans5w,
                    SUM( CASE WHEN e.fans_type = 5 THEN 1 ELSE 0 END ) AS fans10w,
                    e.operate_id AS e_user_id
                FROM
                    operate_vermicelli AS e
                    WHERE
                        FIND_IN_SET(e.operate_id,#{userIds})
                    <if test="dto.beginTime != null">
                         AND   e.record_date >= #{dto.beginTime}
                    </if>
                    <if test="dto.endTime != null">
                         AND e.record_date &lt;= #{dto.endTime}
                    </if>
                GROUP BY
                    e_user_id
            ) e ON u.user_id = e.e_user_id
                LEFT JOIN (
                SELECT
                    COUNT(DISTINCT CASE WHEN cja.first_charge_date IS NOT NULL THEN cja.customer_id END) firstChargeNum,
                    COUNT( DISTINCT CASE WHEN cja.join_amt > 0 THEN customer_id END ) handoverRewardNum,
                    COUNT( DISTINCT customer_id ) handoverNum,
                    operate_id operate_id,
                    up.user_id
                FROM
                    yooa_crm.crm_customer_join_anchor cja
                        LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cja.operate_id
                WHERE
                    `status` = 1
                  AND FIND_IN_SET( operate_id, #{pdUserIds} )
                     <if test="dto.beginTime != null">
                         AND  cja.handover_time >= #{dto.beginTime}
                     </if>
                     <if test="dto.endTime != null">
                         AND cja.handover_time &lt;= #{dto.endTime}
                     </if>
                GROUP BY
                    up.user_id
            ) cja ON cja.user_id = u.user_id
                LEFT JOIN (
                SELECT
                    IFNULL( SUM( CASE WHEN ai.live_status = 0 THEN 1 ELSE 0 END ), 0 ) sumBeginToShow,
                    IFNULL( SUM( CASE WHEN ai.live_status = 1 THEN 1 ELSE 0 END ), 0 ) sumOffAirBroadcast,
                    IFNULL( SUM( CASE WHEN ai.live_status = 2 THEN 1 ELSE 0 END ), 0 ) sumCeaseBroadcasting,
                    IFNULL( SUM( CASE WHEN ai.live_status = 5 THEN 1 ELSE 0 END ), 0 ) toBeDiscontinued,
                    u.user_name,
                    ai.anchor_id,
                    u.user_id userId
                FROM
                    yooa_crm.crm_anchor_info ai
                        LEFT JOIN yooa_system.sys_user u ON u.user_id = ai.operate_id
                WHERE
                    FIND_IN_SET(ai.operate_id,#{userIds})
                GROUP BY
                    u.user_id
            ) ai ON u.user_id = ai.userId
                LEFT JOIN (
                SELECT
                    COUNT( DISTINCT customer_id ) rewardNum,
                    up.user_id,
                    ROUND( IFNULL( SUM( total_amount ), 0 ), 2 ) rewardAmount
                FROM
                    yooa_crm.crm_customer_reward cr
                        INNER JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cr.operate_id
                WHERE
                    FIND_IN_SET(cr.operate_id,#{pdUserIds})
                    <if test="dto.beginTime != null">
                          AND  cr.add_time >= #{dto.beginTime}
                    </if>
                    <if test="dto.endTime != null">
                          AND cr.add_time &lt;= #{dto.endTime}
                     </if>
                GROUP BY cr.operate_id
            ) cr ON cr.user_id = u.user_id
        group by u.user_id

        <choose>
            <when test="dto.filed != null and dto.filed != '' ">
                ORDER BY ${dto.filed}
                <choose>
                    <when test="dto.order != null and dto.order != ''">
                        ${dto.order}
                    </when>
                    <otherwise>
                        DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY rewardAmount DESC
            </otherwise>
        </choose>
    </select>
    <select id="operateDetailedUserLimits" resultType="com.yooa.extend.api.domain.vo.OperationalBusinessDataVo">
        SELECT
        u.user_id       AS id,
        u.nick_name     AS name,
        u.dept_id,
        d.leader,
        CONCAT(d.ancestors_names, '-', d.dept_name) AS ancestors_names,
        CASE WHEN u.user_id = d.leader
        THEN
        (
        SELECT u1.nick_name
        FROM yooa_system.sys_dept AS d1
        LEFT JOIN yooa_system.sys_user AS u1 ON d1.leader = u1.user_id
        WHERE d1.dept_id = d.parent_id
        )
        ELSE
        (
        SELECT u1.nick_name
        FROM yooa_system.sys_user AS u1
        WHERE u1.user_id = d.leader
        )
        END leader_name,
        GROUP_CONCAT(pd_user_id) AS pdUserIds,
        u.nick_name operateName
        FROM yooa_system.sys_user AS u
        LEFT JOIN yooa_system.sys_dept AS d ON u.dept_id = d.dept_id
        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
        <if test="dto.status != null" >
            RIGHT JOIN (
            SELECT * FROM yooa_system.sys_roster
            WHERE
            employee_status = #{dto.status}
            ) AS r ON u.user_id = r.oa_user_id
        </if>
        <where>
            up.pd_user_id IS NOT NULL
            <if test="dto.name != null and dto.name != ''">
                AND u.nick_name like concat('%', #{dto.name}, '%')
            </if>
            <if test="dto.deptId != null and dto.deptId != 0">
                AND FIND_IN_SET(#{dto.deptId},CONCAT(d.ancestors ,',', d.dept_id))
            </if>
                AND d.dept_type = 2
            ${dto.params.dataScope}
        </where>
        GROUP by u.user_id
    </select>

    <select id="operateDetailedDeptLimits"
            resultType="com.yooa.extend.api.domain.vo.OperationalBusinessDataVo">
        SELECT
        d.dept_id       AS id,
        d.dept_name     AS operateName,
        d.leader,
        d.ancestors_names,
        (
        SELECT u1.nick_name
        FROM yooa_system.sys_user AS u1
        WHERE u1.user_id = d.leader
        ) AS leader_name
        FROM (
        SELECT
        d.dept_id,
        d.dept_name,
        GROUP_CONCAT(d1.dept_id) AS juniorIds,
        CONCAT(d.ancestors_names, '-', d.dept_name) AS ancestors_names,
        d.leader
        FROM yooa_system.sys_dept AS d
        LEFT JOIN yooa_system.sys_dept AS d1 ON FIND_IN_SET(d.dept_id,d1.ancestors) OR d.dept_id = d1.dept_id
        <where>
            d.dept_type = 2
            <if test="dto.deptId != null and dto.deptId != 0">
                AND FIND_IN_SET(#{dto.deptId},CONCAT(d.ancestors ,',', d.dept_id))
            </if>
            <if test="dto.deptLevel != null and dto.deptLevel != 0">
                AND d.dept_level = #{dto.deptLevel}
            </if>
        </where>
        GROUP BY d.dept_id
        ) AS d
        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)
        <if test="dto.status != null" >
            RIGHT JOIN (
            SELECT * FROM yooa_system.sys_roster
            WHERE
            employee_status = #{dto.status}
            ) AS r ON u.user_id = r.oa_user_id
        </if>
        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
        <where>
            up.user_id IS NOT NULL
            ${dto.params.dataScope}
        </where>
        GROUP BY d.dept_id
    </select>


    <select id="operateDetailedDeptData" resultType="com.yooa.extend.api.domain.vo.OperationalBusinessDataVo">
        WITH
        tmp_crm_reward_summary AS (
        SELECT
        cr.operate_id,
        COUNT(DISTINCT cr.customer_id) AS reward_num,
        ROUND(SUM(cr.total_amount), 2) AS reward_amount
        FROM yooa_crm.crm_customer_reward cr
        <where>
            <if test="dto.beginTime != null">
                AND cr.add_time >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND cr.add_time &lt;= #{dto.endTime}
            </if>
        </where>
        GROUP BY cr.operate_id,cr.customer_id
        ),
        tmp_join_anchor_summary AS (
        SELECT
        cja.operate_id,
        COUNT(DISTINCT CASE WHEN cja.first_charge_date IS NOT NULL THEN 1 END) AS first_charge_num,
        COUNT(DISTINCT CASE WHEN cja.join_amt > 0 THEN cja.customer_id END) AS handover_reward_num,
        COUNT(DISTINCT cja.customer_id) AS handover_num
        FROM yooa_crm.crm_customer_join_anchor cja
        WHERE cja.status = 1
        <if test="dto.beginTime != null">
            AND cja.handover_time >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND cja.handover_time &lt;= #{dto.endTime}
        </if>
        GROUP BY cja.operate_id,cja.customer_id
        ),
        tmp_fans_summary AS (
        SELECT
        operate_dept_id,
        SUM(CASE WHEN fans_type = 1 THEN 1 ELSE 0 END) AS fans_2h,
        SUM(CASE WHEN fans_type = 2 THEN 1 ELSE 0 END) AS fans_5h,
        SUM(CASE WHEN fans_type = 3 THEN 1 ELSE 0 END) AS fans_5k,
        SUM(CASE WHEN fans_type = 4 THEN 1 ELSE 0 END) AS fans_5w,
        SUM(CASE WHEN fans_type = 5 THEN 1 ELSE 0 END) AS fans_10w
        FROM operate_vermicelli
        <where>
            <if test="dto.beginTime != null">
                AND record_date >= #{dto.beginTime}
            </if>
            <if test="dto.endTime != null">
                AND record_date &lt;= #{dto.endTime}
            </if>
        </where>
        GROUP BY operate_dept_id
        ),
        tmp_live_status_summary AS (
        SELECT
        u.dept_id,
        SUM(CASE WHEN ai.live_status = 0 THEN 1 ELSE 0 END) AS sum_begin_to_show,
        SUM(CASE WHEN ai.live_status = 1 THEN 1 ELSE 0 END) AS sum_off_air_broadcast,
        SUM(CASE WHEN ai.live_status = 2 THEN 1 ELSE 0 END) AS sum_cease_broadcasting,
        SUM(CASE WHEN ai.live_status = 5 THEN 1 ELSE 0 END) AS to_be_discontinued
        FROM yooa_crm.crm_anchor_info ai
        LEFT JOIN yooa_system.sys_user u ON u.user_id = ai.operate_id
        GROUP BY u.dept_id
        )

        SELECT
        d.dept_id  id,
        d.dept_name operateName,

        <!-- 打赏信息 -->
        (
        SELECT SUM(r.reward_num)
        FROM tmp_crm_reward_summary r
        LEFT JOIN yooa_system.sys_user_pd up ON r.operate_id = up.pd_user_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        WHERE FIND_IN_SET(u.dept_id, d.juniorIds)
        ) AS totalRewardNum,
        (
        SELECT SUM(r.reward_amount)
        FROM tmp_crm_reward_summary r
        LEFT JOIN yooa_system.sys_user_pd up ON r.operate_id = up.pd_user_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        WHERE FIND_IN_SET(u.dept_id, d.juniorIds)
        ) AS rewardAmount,
        IFNULL(
        (
        SELECT
        IF(SUM(j.handover_num) = 0, 0,
        ROUND(SUM(j.handover_reward_num) / SUM(j.handover_num) * 100, 4))
        FROM tmp_join_anchor_summary j
        LEFT JOIN yooa_system.sys_user_pd up ON j.operate_id = up.pd_user_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        WHERE FIND_IN_SET(u.dept_id, d.juniorIds)
        ), 0
        ) AS handoverRewardRate,
        IFNULL(
        (
        SELECT
        IF(SUM(j.handover_num) = 0, 0,
        ROUND(SUM(j.first_charge_num) / SUM(j.handover_num) * 100, 4))
        FROM tmp_join_anchor_summary j
        LEFT JOIN yooa_system.sys_user_pd up ON j.operate_id = up.pd_user_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        WHERE FIND_IN_SET(u.dept_id, d.juniorIds)
        ), 0
        ) AS handoverFirstChargeRate,

        <!-- 粉丝信息 -->
        (
        SELECT SUM(f.fans_2h)
        FROM tmp_fans_summary f
        WHERE FIND_IN_SET(f.operate_dept_id, d.juniorIds)
        ) AS fans2h,
        (
        SELECT SUM(f.fans_5h)
        FROM tmp_fans_summary f
        WHERE FIND_IN_SET(f.operate_dept_id, d.juniorIds)
        ) AS fans5h,
        (
        SELECT SUM(f.fans_5k)
        FROM tmp_fans_summary f
        WHERE FIND_IN_SET(f.operate_dept_id, d.juniorIds)
        ) AS fans5k,
        (
        SELECT SUM(f.fans_5w)
        FROM tmp_fans_summary f
        WHERE FIND_IN_SET(f.operate_dept_id, d.juniorIds)
        ) AS fans5w,
        (
        SELECT SUM(f.fans_10w)
        FROM tmp_fans_summary f
        WHERE FIND_IN_SET(f.operate_dept_id, d.juniorIds)
        ) AS fans10w,

        <!-- 交接信息 -->
        (
        SELECT SUM(j.first_charge_num)
        FROM tmp_join_anchor_summary j
        LEFT JOIN yooa_system.sys_user_pd up ON j.operate_id = up.pd_user_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        WHERE FIND_IN_SET(u.dept_id, d.juniorIds)
        ) AS firstChargeNum,
        (
        SELECT SUM(j.handover_reward_num)
        FROM tmp_join_anchor_summary j
        LEFT JOIN yooa_system.sys_user_pd up ON j.operate_id = up.pd_user_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        WHERE FIND_IN_SET(u.dept_id, d.juniorIds)
        ) AS handoverRewardNum,
        (
        SELECT SUM(j.handover_num)
        FROM tmp_join_anchor_summary j
        LEFT JOIN yooa_system.sys_user_pd up ON j.operate_id = up.pd_user_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        WHERE FIND_IN_SET(u.dept_id, d.juniorIds)
        ) AS handoverNum,

        <!-- 直播状态信息 -->
        (
        SELECT SUM(ls.sum_begin_to_show)
        FROM tmp_live_status_summary ls
        WHERE FIND_IN_SET(ls.dept_id, d.juniorIds)
        ) AS sumBeginToShow,
        (
        SELECT SUM(ls.sum_off_air_broadcast)
        FROM tmp_live_status_summary ls
        WHERE FIND_IN_SET(ls.dept_id, d.juniorIds)
        ) AS sumOffAirBroadcast,
        (
        SELECT SUM(ls.sum_cease_broadcasting)
        FROM tmp_live_status_summary ls
        WHERE FIND_IN_SET(ls.dept_id, d.juniorIds)
        ) AS sumCeaseBroadcasting,
        (
        SELECT SUM(ls.to_be_discontinued)
        FROM tmp_live_status_summary ls
        WHERE FIND_IN_SET(ls.dept_id, d.juniorIds)
        ) AS toBeDiscontinued

        FROM (
        SELECT
        d.dept_id,
        u.nick_name,
        d.juniorIds,
        d.dept_name,
        GROUP_CONCAT(up.pd_user_id) AS pd_user_ids
        FROM (
        SELECT
        d.dept_id,
        d.dept_name,
        GROUP_CONCAT(d1.dept_id) AS juniorIds
        FROM yooa_system.sys_dept d
        LEFT JOIN yooa_system.sys_dept d1 ON FIND_IN_SET(d.dept_id, d1.ancestors) OR d.dept_id = d1.dept_id
        GROUP BY d.dept_id
        ) d
        LEFT JOIN yooa_system.sys_user u ON FIND_IN_SET(u.dept_id, d.juniorIds)
        LEFT JOIN yooa_system.sys_user_pd up ON u.user_id = up.user_id
        WHERE FIND_IN_SET(d.dept_id, #{deptIds})
        GROUP BY d.dept_id
        ) d

        <choose>
            <when test="dto.filed != null and dto.filed != '' ">
                ORDER BY ${dto.filed}
                <choose>
                    <when test="dto.order != null and dto.order != ''">
                        ${dto.order}
                    </when>
                    <otherwise>
                        DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY rewardAmount DESC
            </otherwise>
        </choose>
    </select>
    <select id="getOperationalBusinessData" resultType="com.yooa.extend.api.domain.vo.OperationalBusinessDataVo">
        SELECT
        u.id,
        u.name,
        u.dept_id,
        u.leader,
        u.ancestors_names,
        u.leader_name,
        u.pdUserIds,
        u.operateName,
        IFNULL(e.fans2h, 0) AS fans2h,
        IFNULL(e.fans5k, 0) AS fans5k,
        IFNULL(e.fans5w, 0) AS fans5w,
        IFNULL(e.fans10w, 0) AS fans10w,
        IFNULL(cja.firstChargeNum, 0) AS firstChargeNum,
        IFNULL(cja.handoverRewardNum, 0) AS handoverRewardNum,
        IFNULL(cja.handoverNum, 0) AS handoverNum,
        IFNULL(ai.sumBeginToShow, 0) AS beginToShowNum,
        IFNULL(ai.sumOffAirBroadcast, 0) AS offAirBroadcastNum,
        IFNULL(ai.sumCeaseBroadcasting, 0) AS discontinueBroadcastingNum,
        IFNULL(ai.toBeDiscontinued, 0) AS toBeDiscontinued,
        IFNULL(cr.rewardNum, 0) AS totalRewardNum,
        IFNULL(cr.rewardAmount, 0) AS rewardAmount,
        CASE WHEN IFNULL(cja.handoverNum, 0) = 0 THEN 0
        ELSE ROUND(cja.handoverRewardNum / cja.handoverNum * 100, 4)
        END AS handoverRewardRate,
        CASE WHEN IFNULL(cja.handoverNum, 0) = 0 THEN 0
        ELSE ROUND(cja.firstChargeNum / cja.handoverNum * 100, 4)
        END AS handoverFirstChargeRate
        FROM (
        SELECT
        u.user_id AS id,
        u.nick_name AS name,
        u.dept_id,
        d.leader,
        CONCAT(d.ancestors_names, '-', d.dept_name) AS ancestors_names,
        CASE WHEN u.user_id = d.leader
        THEN (SELECT u1.nick_name
        FROM yooa_system.sys_dept d1
        LEFT JOIN yooa_system.sys_user u1 ON d1.leader = u1.user_id
        WHERE d1.dept_id = d.parent_id)
        ELSE (SELECT u1.nick_name
        FROM yooa_system.sys_user u1
        WHERE u1.user_id = d.leader)
        END leader_name,
        GROUP_CONCAT(up.pd_user_id) AS pdUserIds,
        u.nick_name AS operateName
        FROM yooa_system.sys_user u
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN yooa_system.sys_user_pd up ON u.user_id = up.user_id
        <if test="dto.status != null">
            RIGHT JOIN (
            SELECT oa_user_id FROM yooa_system.sys_roster
            WHERE employee_status = #{dto.status}
            ) r ON u.user_id = r.oa_user_id
        </if>
        <where>
            up.pd_user_id IS NOT NULL
            <if test="dto.name != null and dto.name != ''">
                AND u.nick_name LIKE CONCAT('%', #{dto.name}, '%')
            </if>
            <if test="dto.deptId != null and dto.deptId != 0">
                AND FIND_IN_SET(#{dto.deptId}, CONCAT(d.ancestors, ',', d.dept_id))
            </if>
            AND d.dept_type = 2
            ${dto.params.dataScope}
        </where>
        GROUP BY u.user_id
        ) u
        LEFT JOIN (
        SELECT
        operate_id AS user_id,
        SUM(fans_type = 1) AS fans2h,
        SUM(fans_type = 3) AS fans5k,
        SUM(fans_type = 4) AS fans5w,
        SUM(fans_type = 5) AS fans10w
        FROM operate_vermicelli
        <where>
            <if test="dto.beginTime != null">record_date >= #{dto.beginTime}</if>
            <if test="dto.endTime != null">AND record_date &lt;= #{dto.endTime}</if>
        </where>
        GROUP BY operate_id
        ) e ON u.id = e.user_id

        LEFT JOIN (
        SELECT
        up.user_id,
        COUNT(DISTINCT CASE WHEN cja.first_charge_date IS NOT NULL THEN cja.customer_id END) AS firstChargeNum,
        COUNT(DISTINCT CASE WHEN cja.join_amt > 0 THEN cja.customer_id END) AS handoverRewardNum,
        COUNT(DISTINCT cja.customer_id) AS handoverNum
        FROM yooa_crm.crm_customer_join_anchor cja
        JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.operate_id
        <where>
            cja.status = 1
            <if test="dto.beginTime != null">AND cja.handover_time >= #{dto.beginTime}</if>
            <if test="dto.endTime != null">AND cja.handover_time &lt;= #{dto.endTime}</if>
        </where>
        GROUP BY up.user_id
        ) cja ON u.id = cja.user_id
        LEFT JOIN (
        SELECT
        operate_id AS user_id,
        SUM(live_status = 0) AS sumBeginToShow,
        SUM(live_status = 1) AS sumOffAirBroadcast,
        SUM(live_status = 2) AS sumCeaseBroadcasting,
        SUM(live_status = 5) AS toBeDiscontinued
        FROM yooa_crm.crm_anchor_info
        GROUP BY operate_id
        ) ai ON u.id = ai.user_id
        LEFT JOIN (
        SELECT
        up.user_id,
        COUNT(DISTINCT cr.customer_id) AS rewardNum,
        ROUND(SUM(cr.total_amount), 2) AS rewardAmount
        FROM yooa_crm.crm_customer_reward cr
        JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cr.operate_id
        <where>
            <if test="dto.beginTime != null">AND cr.add_time >= #{dto.beginTime}</if>
            <if test="dto.endTime != null">AND cr.add_time &lt;= #{dto.endTime}</if>
        </where>
        GROUP BY up.user_id
        ) cr ON u.id = cr.user_id
        <choose>
            <when test="dto.filed != null and dto.filed != '' ">
                ORDER BY ${dto.filed}
                <choose>
                    <when test="dto.order != null and dto.order != ''">
                        ${dto.order}
                    </when>
                    <otherwise>
                        DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY rewardAmount DESC
            </otherwise>
        </choose>
    </select>
    <select id="OperateDetailedUserDataTest"
            resultType="com.yooa.extend.api.domain.vo.OperationalBusinessDataVo">
        SELECT
        IFNULL( e.fans2h, 0 ) AS fans2h,
        IFNULL( e.fans5k, 0 ) AS fans5h,
        IFNULL( e.fans5w, 0 ) AS fans5k,
        IFNULL( e.fans10w, 0 ) AS fans10k,
        IFNULL( e.fans5w, 0 ) AS fans5w,
        IFNULL( cja.firstChargeNum, 0 ) firstChargeNum,
        IFNULL( cja.handoverRewardNum, 0 ) handoverRewardNum,
        IFNULL( cja.handoverNum, 0 ) handoverNum,
        IFNULL( ai.sumBeginToShow, 0 ) sumBeginToShow,
        IFNULL( ai.sumOffAirBroadcast, 0 ) sumOffAirBroadcast,
        IFNULL( ai.sumCeaseBroadcasting, 0 ) sumCeaseBroadcasting,
        IFNULL( ai.toBeDiscontinued, 0 ) toBeDiscontinued,
        IFNULL( cr.rewardNum, 0 ) rewardNum,
        IFNULL( cr.rewardAmount, 0 ) rewardAmount,
        CASE
        WHEN IFNULL(cja.handoverNum, 0) = 0 THEN 0
        ELSE ROUND(cja.handoverRewardNum / cja.handoverNum * 100, 4)
        END AS handoverRewardRate,
        CASE
        WHEN IFNULL(cja.handoverNum, 0) = 0 THEN 0
        ELSE ROUND(cja.firstChargeNum / cja.handoverNum * 100, 4)
        END AS handoverFirstChargeRate,
        u.user_id id,
        u.nick_name operateName
        FROM
        (
        SELECT
        u.user_id,
        u.nick_name,
        GROUP_CONCAT( up.pd_user_id ) AS pd_user_ids
        FROM
        yooa_system.sys_user AS u
        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
        WHERE
        FIND_IN_SET(
        u.user_id,
        #{userIds})
        GROUP BY
        u.user_id
        ) AS u
        LEFT JOIN (
        SELECT
        SUM( CASE WHEN e.fans_type = 1 THEN 1 ELSE 0 END ) AS fans2h,
        SUM( CASE WHEN e.fans_type = 3 THEN 1 ELSE 0 END ) AS fans5k,
        SUM( CASE WHEN e.fans_type = 4 THEN 1 ELSE 0 END ) AS fans5w,
        SUM( CASE WHEN e.fans_type = 5 THEN 1 ELSE 0 END ) AS fans10w,
        e.operate_id AS e_user_id
        FROM
        operate_vermicelli AS e
        WHERE
        FIND_IN_SET(e.operate_id,#{userIds})
        <if test="dto.beginTime != null">
            AND   e.record_date >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND e.record_date &lt;= #{dto.endTime}
        </if>
        GROUP BY
        e_user_id
        ) e ON u.user_id = e.e_user_id
        LEFT JOIN (
        SELECT
        count( CASE WHEN cja.first_charge_date IS NOT NULL THEN 1 END ) firstChargeNum,
        count( DISTINCT CASE WHEN cja.join_amt > 0 THEN customer_id END ) handoverRewardNum,
        count( DISTINCT customer_id ) handoverNum,
        operate_id operate_id,
        up.user_id
        FROM
        yooa_crm.crm_customer_join_anchor cja
        LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cja.operate_id
        WHERE
        `status` = 1
        AND FIND_IN_SET( operate_id, #{pdUserIds} )
        <if test="dto.beginTime != null">
            AND  cja.handover_time >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND cja.handover_time &lt;= #{dto.endTime}
        </if>
        GROUP BY
        up.user_id
        ) cja ON cja.user_id = u.user_id
        LEFT JOIN (
        SELECT
        IFNULL( SUM( CASE WHEN ai.live_status = 0 THEN 1 ELSE 0 END ), 0 ) sumBeginToShow,
        IFNULL( SUM( CASE WHEN ai.live_status = 1 THEN 1 ELSE 0 END ), 0 ) sumOffAirBroadcast,
        IFNULL( SUM( CASE WHEN ai.live_status = 2 THEN 1 ELSE 0 END ), 0 ) sumCeaseBroadcasting,
        IFNULL( SUM( CASE WHEN ai.live_status = 5 THEN 1 ELSE 0 END ), 0 ) toBeDiscontinued,
        u.user_name,
        ai.anchor_id,
        u.user_id userId
        FROM
        yooa_crm.crm_anchor_info ai
        LEFT JOIN yooa_system.sys_user u ON u.user_id = ai.operate_id
        WHERE
        FIND_IN_SET(ai.operate_id,#{userIds})
        GROUP BY
        u.user_id
        ) ai ON u.user_id = ai.userId
        LEFT JOIN (
        SELECT
        COUNT( DISTINCT customer_id ) rewardNum,
        up.user_id,
        ROUND( IFNULL( SUM( total_amount ), 0 ), 2 ) rewardAmount
        FROM
        yooa_crm.crm_customer_reward cr
        INNER JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cr.operate_id
        WHERE
        FIND_IN_SET(cr.operate_id,#{pdUserIds})
        <if test="dto.beginTime != null">
            AND  cr.add_time >= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND cr.add_time &lt;= #{dto.endTime}
        </if>
        GROUP BY cr.operate_id
        ) cr ON cr.user_id = u.user_id
        group by u.user_id

        <choose>
            <when test="dto.filed != null and dto.filed != '' ">
                ORDER BY ${dto.filed}
                <choose>
                    <when test="dto.order != null and dto.order != ''">
                        ${dto.order}
                    </when>
                    <otherwise>
                        DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY rewardAmount DESC
            </otherwise>
        </choose>

    </select>


    <select id="getOperationalBusinessDeptData" resultType="com.yooa.extend.api.domain.vo.OperationalBusinessDataVo">
        SELECT
        d.dept_id AS id,
        d.dept_name AS operateName,
        d.leader,
        d.ancestors_names,
        u1.nick_name AS leader_name,
        IFNULL(cr.totalRewardNum, 0) AS totalRewardNum,
        IFNULL(cr.rewardAmount, 0) AS rewardAmount,
        IFNULL(cja.handoverRewardNum, 0) AS handoverRewardNum,
        IFNULL(cja.handoverNum, 0) AS handoverNum,
        IFNULL(cja.firstChargeNum, 0) AS firstChargeNum,
        IFNULL(v.fans2h, 0) AS fans2h,
        IFNULL(v.fans5k, 0) AS fans5k,
        IFNULL(v.fans5w, 0) AS fans5w,
        IFNULL(v.fans10w, 0) AS fans10w,
        IFNULL(ai.sumBeginToShow, 0) AS beginToShowNum,
        IFNULL(ai.sumOffAirBroadcast, 0) AS offAirBroadcastNum,
        IFNULL(ai.sumCeaseBroadcasting, 0) AS discontinueBroadcastingNum,
        IFNULL(ai.toBeDiscontinued, 0) AS toBeDiscontinued,
        CASE WHEN IFNULL(cja.handoverNum, 0) = 0 THEN 0
        ELSE ROUND(cja.handoverRewardNum / cja.handoverNum * 100, 4)
        END AS handoverRewardRate,
        CASE WHEN IFNULL(cja.handoverNum, 0) = 0 THEN 0
        ELSE ROUND(cja.firstChargeNum / cja.handoverNum * 100, 4)
        END AS handoverFirstChargeRate
        FROM (
        SELECT
        d.dept_id,
        d.dept_name,
        d.leader,
        CONCAT(d.ancestors_names, '-', d.dept_name) AS ancestors_names,
        GROUP_CONCAT(DISTINCT d1.dept_id) AS juniorIds
        FROM yooa_system.sys_dept d
        LEFT JOIN yooa_system.sys_dept d1
        ON FIND_IN_SET(d.dept_id, d1.ancestors) OR d.dept_id = d1.dept_id
        <where>
            d.dept_type = 2
            <if test="dto.deptId != null and dto.deptId != 0">
                AND FIND_IN_SET(#{dto.deptId}, CONCAT(d.ancestors, ',', d.dept_id))
            </if>
            <if test="dto.deptLevel != null and dto.deptLevel != 0">
                AND d.dept_level = #{dto.deptLevel}
            </if>
        </where>
        GROUP BY d.dept_id
        ) d
        LEFT JOIN yooa_system.sys_user u1 ON u1.user_id = d.leader
        <!-- 打赏数据 -->
        LEFT JOIN (
        SELECT
        dept_id,
        COUNT(*) AS totalRewardNum,
        ROUND(SUM(total_amount), 2) AS rewardAmount
        FROM (
        SELECT
        u.dept_id,
        cr.customer_id,
        cr.operate_id,
        SUM(cr.total_amount) AS total_amount
        FROM yooa_crm.crm_customer_reward cr
        JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cr.operate_id
        JOIN yooa_system.sys_user u ON u.user_id = up.user_id
        <where>
            <if test="dto.beginTime != null">cr.add_time >= #{dto.beginTime}</if>
            <if test="dto.endTime != null">AND cr.add_time &lt;= #{dto.endTime}</if>
        </where>
        GROUP BY cr.customer_id, cr.operate_id
        ) t
        GROUP BY dept_id
        ) cr ON FIND_IN_SET(cr.dept_id, d.juniorIds)
        <!-- 交接数据 -->
        LEFT JOIN (
        SELECT
        dept_id,
        COUNT(*) AS handoverNum,
        COUNT(CASE WHEN first_charge_date IS NOT NULL THEN 1 END) AS firstChargeNum,
        COUNT(CASE WHEN join_amt > 0 THEN 1 END) AS handoverRewardNum
        FROM (
        SELECT
        u.dept_id,
        cja.customer_id,
        cja.operate_id,
        MAX(cja.join_amt) AS join_amt,
        MIN(cja.first_charge_date) AS first_charge_date
        FROM yooa_crm.crm_customer_join_anchor cja
        JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.operate_id
        JOIN yooa_system.sys_user u ON u.user_id = up.user_id
        WHERE cja.status = 1
        <if test="dto.beginTime != null">AND cja.receive_time >= #{dto.beginTime}</if>
        <if test="dto.endTime != null">AND cja.receive_time &lt;= #{dto.endTime}</if>
        GROUP BY cja.customer_id, cja.operate_id
        ) t
        GROUP BY dept_id
        ) cja ON FIND_IN_SET(cja.dept_id, d.juniorIds)
        <!-- 粉丝达成 -->
        LEFT JOIN (
        SELECT
        operate_dept_id,
        SUM(fans_type = 1) AS fans2h,
        SUM(fans_type = 3) AS fans5k,
        SUM(fans_type = 4) AS fans5w,
        SUM(fans_type = 5) AS fans10w
        FROM operate_vermicelli
        <where>
            <if test="dto.beginTime != null">record_date >= #{dto.beginTime}</if>
            <if test="dto.endTime != null">AND record_date &lt;= #{dto.endTime}</if>
        </where>
        GROUP BY operate_dept_id
        ) v ON FIND_IN_SET(v.operate_dept_id, d.juniorIds)
        <!-- 直播状态 -->
        LEFT JOIN (
        SELECT
        u.dept_id,
        SUM(ai.live_status = 0) AS sumBeginToShow,
        SUM(ai.live_status = 1) AS sumOffAirBroadcast,
        SUM(ai.live_status = 2) AS sumCeaseBroadcasting,
        SUM(ai.live_status = 5) AS toBeDiscontinued
        FROM yooa_crm.crm_anchor_info ai
        JOIN yooa_system.sys_user u ON u.user_id = ai.operate_id
        GROUP BY u.dept_id
        ) ai ON FIND_IN_SET(ai.dept_id, d.juniorIds)
        <if test="dto.status != null">
            JOIN (
            SELECT DISTINCT u.dept_id
            FROM yooa_system.sys_roster r
            JOIN yooa_system.sys_user u ON u.user_id = r.oa_user_id
            WHERE r.employee_status = #{dto.status}
            ) roster ON FIND_IN_SET(roster.dept_id, d.juniorIds)
        </if>
        <where>
            ${dto.params.dataScope}
        </where>
        GROUP BY d.dept_id
        <choose>
            <when test="dto.filed != null and dto.filed != '' ">
                ORDER BY ${dto.filed}
                <choose>
                    <when test="dto.order != null and dto.order != ''">
                        ${dto.order}
                    </when>
                    <otherwise>
                        DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY rewardAmount DESC
            </otherwise>
        </choose>

    </select>

    <select id="getUserCount" resultType="java.lang.Integer">
        SELECT
        SUM( u.userIds )
        FROM(
        SELECT
        COUNT(DISTINCT u.user_id)userIds
        FROM yooa_system.sys_user AS u
        LEFT JOIN yooa_system.sys_dept AS d ON u.dept_id = d.dept_id
        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id
        <if test="dto.status != null">
            RIGHT JOIN (
            SELECT * FROM yooa_system.sys_roster
            WHERE
            employee_status = #{dto.status}
            ) AS r ON u.user_id = r.oa_user_id
        </if>
        <where>
            up.pd_user_id IS NOT NULL
            <if test="dto.name != null and dto.name != ''">
                AND u.nick_name like concat('%', #{dto.name}, '%')
            </if>
            <if test="dto.deptId != null and dto.deptId != 0">
                AND FIND_IN_SET(#{dto.deptId},CONCAT(d.ancestors ,',', d.dept_id))
            </if>
            AND d.dept_type = 2
            ${dto.params.dataScope}
        </where>
        GROUP by u.user_id
        )u
    </select>

    <!--    <select id="operateDetailedDeptData" resultType="com.yooa.extend.api.domain.vo.OperateDetailVo">-->
<!--        SELECT-->
<!--        &lt;!&ndash;粉丝登记&ndash;&gt;-->
<!--        (-->
<!--        SELECT-->
<!--        JSON_OBJECT('fans2h', IFNULL(SUM( CASE WHEN e.fans_type = 1 THEN 1 ELSE 0 END ),0),-->
<!--        'fans5h', IFNULL(SUM( CASE WHEN e.fans_type = 2 THEN 1 ELSE 0 END ),0),-->
<!--        'fans5k', IFNULL(SUM( CASE WHEN e.fans_type = 3 THEN 1 ELSE 0 END ),0),-->
<!--        'fans5w', IFNULL(SUM( CASE WHEN e.fans_type = 4 THEN 1 ELSE 0 END ),0),-->
<!--        'fans10w', IFNULL(SUM( CASE WHEN e.fans_type = 5 THEN 1 ELSE 0 END ),0))-->
<!--        FROM-->
<!--        operate_vermicelli AS e-->
<!--        WHERE-->
<!--        FIND_IN_SET(e.operate_dept_id,d.juniorIds)-->
<!--        <if test="dto.beginTime != null">-->
<!--            AND e.record_date >= #{dto.beginTime}-->
<!--        </if>-->
<!--        <if test="dto.endTime != null">-->
<!--            AND e.record_date &lt;= #{dto.endTime}-->
<!--        </if>-->
<!--        )                                                             AS fans_info,-->
<!--        &lt;!&ndash;交接数据&ndash;&gt;-->
<!--        (-->
<!--        SELECT JSON_OBJECT('firstChargeNum',SUM(r.firstChargeNum),-->
<!--        'handoverRewardNum', SUM(r.handoverRewardNum),-->
<!--        'handoverNum',SUM(handoverNum))-->
<!--        FROM (-->
<!--        SELECT-->
<!--        COUNT( CASE WHEN cja.first_charge_date IS NOT NULL-->
<!--        <if test="dto.beginTime != null">-->
<!--            AND cja.first_charge_date >= #{dto.beginTime}-->
<!--        </if>-->
<!--        <if test="dto.endTime != null">-->
<!--            AND cja.first_charge_date &lt;= #{dto.endTime}-->
<!--        </if>-->
<!--            THEN 1 END ) firstChargeNum,-->
<!--        COUNT( DISTINCT CASE WHEN cja.join_amt > 0-->
<!--        <if test="dto.beginTime != null">-->
<!--            AND cja.handover_time >= #{dto.beginTime}-->
<!--        </if>-->
<!--        <if test="dto.endTime != null">-->
<!--            AND cja.handover_time &lt;= #{dto.endTime}-->
<!--        </if>-->
<!--            THEN customer_id END ) handoverRewardNum,-->
<!--        COUNT( DISTINCT CASE-->
<!--        <if test="dto.beginTime != null">-->
<!--            WHEN cja.handover_time >= #{dto.beginTime}-->
<!--        </if>-->
<!--        <if test="dto.endTime != null">-->
<!--            AND cja.handover_time &lt;= #{dto.endTime}-->
<!--        </if>-->
<!--        THEN cja.customer_id END ) handoverNum,-->
<!--        operate_id operate_id,-->
<!--        up.user_id,-->
<!--        u.dept_id-->
<!--        FROM-->
<!--        yooa_crm.crm_customer_join_anchor cja-->
<!--        LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cja.operate_id-->
<!--        LEFT JOIN yooa_system.sys_user AS u ON up.user_id = u.user_id-->
<!--        WHERE-->
<!--        cja.status = 1-->
<!--        <if test="dto.beginTime != null">-->
<!--            AND  cja.handover_time >= #{dto.beginTime}-->
<!--        </if>-->
<!--        <if test="dto.endTime != null">-->
<!--            AND cja.handover_time &lt;= #{dto.endTime}-->
<!--        </if>-->
<!--        GROUP BY-->
<!--        u.dept_id-->
<!--        )r-->
<!--        WHERE FIND_IN_SET(r.dept_id,d.juniorIds) ) joinAnchorInfo ,-->
<!--        &lt;!&ndash;打赏数据&ndash;&gt;-->
<!--        (-->
<!--        SELECT JSON_OBJECT('rewardNum',SUM(cr.rewardNum),-->
<!--        'rewardAmount',SUM(cr.rewardAmount))-->
<!--        FROM (-->
<!--        SELECT-->
<!--        COUNT( DISTINCT customer_id ) rewardNum,-->
<!--        up.user_id,-->
<!--        ROUND( IFNULL( SUM( total_amount ), 0 ), 2 ) rewardAmount,-->
<!--        u.dept_id-->
<!--        FROM-->
<!--        yooa_crm.crm_customer_reward cr-->
<!--        LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cr.operate_id-->
<!--        LEFT JOIN yooa_system.sys_user AS u ON up.user_id = u.user_id-->
<!--        WHERE FIND_IN_SET(u.dept_id,d.juniorIds)-->
<!--        <if test="dto.beginTime != null">-->
<!--            AND  cr.add_time >= #{dto.beginTime}-->
<!--        </if>-->
<!--        <if test="dto.endTime != null">-->
<!--            AND cr.add_time &lt;= #{dto.endTime}-->
<!--        </if>-->
<!--        GROUP BY u.dept_id-->
<!--        )cr-->
<!--        )         rewardInfo,-->
<!--        &lt;!&ndash;直播状态数据&ndash;&gt;-->
<!--        (-->
<!--        SELECT-->
<!--        JSON_OBJECT(-->
<!--        'sumBeginToShow',-->
<!--        IFNULL( SUM( CASE WHEN ai.live_status = 0 THEN 1 ELSE 0 END ), 0 ),-->
<!--        'sumOffAirBroadcast',-->
<!--        IFNULL( SUM( CASE WHEN ai.live_status = 1 THEN 1 ELSE 0 END ), 0 ),-->
<!--        'sumCeaseBroadcasting',-->
<!--        IFNULL( SUM( CASE WHEN ai.live_status = 2 THEN 1 ELSE 0 END ), 0 ),-->
<!--        'toBeDiscontinued',-->
<!--        IFNULL( SUM( CASE WHEN ai.live_status = 5 THEN 1 ELSE 0 END ), 0 )-->
<!--        )-->
<!--        FROM-->
<!--        yooa_crm.crm_anchor_info ai-->
<!--        LEFT JOIN yooa_system.sys_user u ON u.user_id = ai.operate_id-->
<!--        WHERE-->
<!--        FIND_IN_SET( u.dept_id, d.juniorIds )-->
<!--        )       liveStatusInfo,-->
<!--        d.dept_id    id-->
<!--        FROM (-->
<!--        SELECT-->
<!--        d.dept_id,-->
<!--        d.juniorIds                 AS juniorIds,-->
<!--        GROUP_CONCAT(pd_user_id)    AS pd_user_ids-->
<!--        FROM (-->
<!--        SELECT-->
<!--        d.dept_id,-->
<!--        GROUP_CONCAT(d1.dept_id) AS juniorIds-->
<!--        FROM yooa_system.sys_dept AS d-->
<!--        LEFT JOIN yooa_system.sys_dept AS d1 ON FIND_IN_SET(d.dept_id,d1.ancestors) OR d.dept_id = d1.dept_id-->
<!--        GROUP BY d.dept_id-->
<!--        ) AS d-->
<!--        LEFT JOIN yooa_system.sys_user AS u ON FIND_IN_SET(u.dept_id,d.juniorIds)-->
<!--        LEFT JOIN yooa_system.sys_user_pd AS up ON u.user_id = up.user_id-->
<!--        WHERE-->
<!--        FIND_IN_SET(d.dept_id,#{deptIds})-->
<!--        GROUP BY d.dept_id-->
<!--        ) AS d-->
<!--    </select>-->

    <sql id="getDeptJuniorIds">    <!--获取下级部门ID集-->
        SELECT d.dept_id,d.dept_name,GROUP_CONCAT(d1.dept_id) AS juniorIds,d.ancestors_names,d.leader
        FROM yooa_system.sys_dept AS d
        LEFT JOIN yooa_system.sys_dept AS d1 ON FIND_IN_SET(d.dept_id,d1.ancestors) OR d.dept_id = d1.dept_id
        <where>
            d.del_flag = 0
            <if test="dto.deptId != null and dto.deptId != 0">
                AND d.dept_id = #{dto.deptId}
            </if>
            <if test="dto.selType != null and dto.selType != 0">
                AND d.dept_type = #{dto.selType}
            </if>
            <if test="dto.hierarchy != null and dto.hierarchy != 0">
                AND d.hierarchy = #{dto.hierarchy}
            </if>
            <if test="dto.parentId != null and dto.parentId != 0">
                AND d.parent_id = #{dto.parentId}
            </if>
            <if test="dto.findInSetDeptId != null and dto.findInSetDeptId != 0">
                AND (FIND_IN_SET(#{dto.findInSetDeptId},d.ancestors) or d.dept_id = #{dto.findInSetDeptId})
            </if>
        </where>
        GROUP BY d.dept_id
    </sql>

</mapper>
