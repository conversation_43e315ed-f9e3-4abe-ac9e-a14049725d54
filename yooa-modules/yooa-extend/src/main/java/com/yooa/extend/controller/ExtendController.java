package com.yooa.extend.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.security.annotation.InnerAuth;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.extend.api.domain.ExtendTargetProgress;
import com.yooa.extend.api.domain.ExtendTargetProgressHour;
import com.yooa.extend.api.domain.dto.CommonalityDto;
import com.yooa.extend.api.domain.dto.ExtendDetailedDto;
import com.yooa.extend.api.domain.query.OperateBusinessQuery;
import com.yooa.extend.api.domain.vo.DeptRankingVo;
import com.yooa.extend.api.domain.vo.DetailedVo;
import com.yooa.extend.api.domain.vo.OperationalBusinessDataVo;
import com.yooa.extend.api.domain.vo.PeopleRankingVo;
import com.yooa.extend.service.ExtendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 *
 */
@Validated
@RestController
@RequestMapping("extend")
public class ExtendController extends BaseController {

    @Autowired
    private ExtendService extendService;

    /**
     * 数据统计-推广部总业绩排行
     */
    @GetMapping("/extendPerformanceTotalRanking")
    public AjaxResult extendPerformanceTotalRanking(CommonalityDto commonalityDto) {
        return AjaxResult.success(extendService.extendPerformanceTotalRanking(commonalityDto));
    }

    /**
     * 数据统计-推广部各指标按时间分组
     */
    @GetMapping("/extendIndexTimeGroup")
    public AjaxResult extendIndexTimeGroup(CommonalityDto commonalityDto) {
        return AjaxResult.success(extendService.extendIndexTimeGroup(commonalityDto, SecurityUtils.getLoginUser().getSysUser().getDept()));
    }

    /**
     * 数据统计-推广部各指标环比同比
     */
    @GetMapping("/extendIndexCompare")
    public AjaxResult extendIndexCompare(CommonalityDto commonalityDto) {
        return AjaxResult.success(extendService.extendIndexCompare(commonalityDto));
    }

    /**
     * 数据统计-推广部全部指标环比同比
     */
    @GetMapping("/extendIndexCompareAll")
    public AjaxResult extendIndexCompareAll(CommonalityDto commonalityDto) {
        return AjaxResult.success(extendService.extendIndexCompareAll(commonalityDto));
    }

    /**
     * 数据统计-推广部各指标排名和占比
     */
    @GetMapping("/extendIndexCompareRanking")
    public AjaxResult extendIndexCompareRanking(CommonalityDto commonalityDto) {
        return AjaxResult.success(extendService.extendIndexCompareRanking(commonalityDto));
    }

    /**
     * 数据统计-推广部全部指标转换
     * <p>
     * 转换规则:新注册：注册/好友
     * 优质转换：优质/注册
     * 交接转换：交接/注册
     * 二交转换：二交/注册
     * 首充转换：首充/注册
     */
    @GetMapping("/extendConvert")
    public AjaxResult extendConvert(CommonalityDto commonalityDto) {
        return AjaxResult.success(extendService.extendConvert(commonalityDto));
    }

    /**
     * 数据统计-推广部全部转换率对比图
     */
    @GetMapping("/extendConvertGraph")
    public AjaxResult extendConvertGraph(CommonalityDto commonalityDto) {
        return AjaxResult.success(extendService.extendConvertGraph(commonalityDto));
    }

    /**
     * 推广排名-推广各指标个人排行
     */
    @GetMapping("/extendPeopleRanking")
    public AjaxResult extendPeopleRanking(CommonalityDto commonalityDto) {
        Map<String, List<PeopleRankingVo>> map = extendService.extendPeopleRanking(commonalityDto)
                .stream().collect(Collectors.groupingBy(PeopleRankingVo::getFieldsName, Collectors.toList()));
        return AjaxResult.success(map);
    }

    /**
     * 推广排名-推广各指标部门排行
     */
    @GetMapping("/extendDeptRanking")
    public AjaxResult extendDeptRanking(CommonalityDto commonalityDto) {
        Map<String, List<DeptRankingVo>> map = extendService.extendDeptRanking(commonalityDto)
                .stream().collect(Collectors.groupingBy(DeptRankingVo::getFieldsName, Collectors.toList()));
        return AjaxResult.success(map);
    }

    /**
     * 排名详情-我的排名
     */
    @GetMapping("/myUserRanking")
    public AjaxResult myUserRanking(CommonalityDto commonalityDto) {
        return AjaxResult.success(extendService.myUserRanking(commonalityDto, SecurityUtils.getLoginUser().getSysUser()));
    }

    /**
     * 排名详情-我的部门排名
     */
    @GetMapping("/myDeptRanking")
    public AjaxResult myDeptRanking(CommonalityDto commonalityDto) {
        return AjaxResult.success(extendService.myDeptRanking(commonalityDto, SecurityUtils.getLoginUser().getSysUser()));
    }

    /**
     * 排名详情-用户排名分页
     */
    @GetMapping("/userRankingPage")
    public AjaxResult userRankingPage(Page<?> page, CommonalityDto commonalityDto) {
        return AjaxResult.success(extendService.userRankingPage(page, commonalityDto));
    }

    /**
     * 排名详情-部门排名分页
     */
    @GetMapping("/deptRankingPage")
    public AjaxResult deptRankingPage(Page<?> page, CommonalityDto commonalityDto) {
        return AjaxResult.success(extendService.deptRankingPage(page, commonalityDto));
    }

    /**
     * 我的工作台-个人总业绩排名
     *
     * @param page           分页条件
     * @param commonalityDto 查询条件
     */
    @GetMapping("/workbenchMyUserRanking")
    public AjaxResult workbenchMyUserRanking(Page<?> page, CommonalityDto commonalityDto) {
        return AjaxResult.success(extendService.workbenchMyUserRanking(page, commonalityDto, SecurityUtils.getLoginUser().getSysUser()));
    }

    /**
     * 我的工作台-部门总业绩排名
     *
     * @param commonalityDto 查询条件
     */
    @GetMapping("/workbenchMyDeptRanking")
    public AjaxResult workbenchMyDeptRanking(Page<?> page, CommonalityDto commonalityDto) {
        return AjaxResult.success(extendService.workbenchMyDeptRanking(page, commonalityDto, SecurityUtils.getLoginUser().getSysUser()));
    }

    /**
     * 员工详情-员工数据
     */
    @GetMapping("/employeeDetailsData")
    public AjaxResult employeeDetailsData(@RequestParam("id") Long id) {
        return AjaxResult.success(extendService.employeeDetailsData(id));
    }

    /**
     * 员工详情-部门数据
     */
    @GetMapping("/deptDetailsData")
    public AjaxResult deptDetailsData(@RequestParam("id") Long id) {
        return AjaxResult.success(extendService.deptDetailsData(id));
    }

    /**
     * 推广业务数据-数据清单
     */
    @GetMapping("/selectDataExtendDetailed")
    public AjaxResult selectDataExtendDetailed(Page<?> page, ExtendDetailedDto detailedDto) {
        return AjaxResult.success(extendService.selectDataExtendDetailed(page, detailedDto));
    }

    /**
     * 推广业务数据-导出推广数据清单
     */
    @PostMapping("/exportDataExtendDetailed")
    public void exportDataExtendDetailed(HttpServletResponse response, @Valid ExtendDetailedDto detailedDto) {
        List<DetailedVo> list = extendService.selectDataExtendDetailed(new Page<>().setSize(-1), detailedDto).getRecords();      // 不传分页查所有
        ExcelUtil<DetailedVo> util = new ExcelUtil<>(DetailedVo.class);
        util.exportExcel(response, list, "推广数据清单");
    }

    /**
     * 员工详情-本月排名详情
     */
    @PostMapping("/employeeMonthRanking")
    public AjaxResult employeeMonthRanking(@RequestBody CommonalityDto dto) {
        return AjaxResult.success(extendService.employeeMonthRanking(dto));
    }

//    /**
//     * 数据统计-小组/团队/部门数据
//     */
//    @GetMapping("/getGroupDataPerformance")
//    public AjaxResult getGroupDataPerformance(Page page, CommonDataStatisticsQuery commonDataStatisticsQuery){
//        return success(extendService.getGroupDataPerformance(page,commonDataStatisticsQuery));
//    }

//    /**
//     * 运营业务数据
//     */
//    @GetMapping("/getOperationalBusinessData")
//    public AjaxResult getOperationalBusinessData(Page page, OperateBusinessQuery query){
//        return success(page.setRecords(extendService.getOperationalBusinessData(page,query)));
//    }

    /**
     * 运营业务数据
     */
    @GetMapping("/getOperationalBusinessData")
    public AjaxResult getOperationalBusinessData(Page page,@Valid  OperateBusinessQuery query){
        return success(extendService.getOperationalBusinessData(page,query));
    }

    /**
     * 运营业务数据导出
     */
    @PostMapping("/getOperationalBusinessData/export")
    public void getOperationalBusinessDataExport(HttpServletResponse response, OperateBusinessQuery query){
        List<OperationalBusinessDataVo> list = extendService.getOperationalBusinessData(new Page<>().setSize(-1), query).getRecords();      // 不传分页查所有
        ExcelUtil<OperationalBusinessDataVo> util = new ExcelUtil<>(OperationalBusinessDataVo.class);
        util.exportExcel(response, list, "运营数据清单");
    }

    /**
     * 批量新增每日数据汇总
     */
    @InnerAuth
    @PostMapping("/insertAllTargetProgress")
    public R<Integer> insertAllTargetProgress(@RequestBody List<ExtendTargetProgress> list) {
        return R.ok(extendService.insertAllTargetProgress(list));
    }

    /**
     * 批量修改每日数据汇总
     */
    @InnerAuth
    @PostMapping("/updateAllTargetProgress")
    public R<Integer> updateAllTargetProgress(@RequestBody List<ExtendTargetProgress> list) {
        return R.ok(extendService.updateAllTargetProgress(list));
    }

    /**
     * 批量新增每小时数据汇总
     */
    @InnerAuth
    @PostMapping("/insertAllTargetProgressHour")
    public R<Integer> insertAllTargetProgressHour(@RequestBody List<ExtendTargetProgressHour> list) {
        return R.ok(extendService.insertAllTargetProgressHour(list));
    }

    /**
     * 删除超过2天以前的每小时数据汇总
     */
    @InnerAuth
    @PostMapping("/deleteTargetProgressHour")
    public R<Integer> deleteTargetProgressHour() {
        return R.ok(extendService.deleteTargetProgressHour());
    }

    /**
     * 获取每日数据汇总的最后一条数据
     */
    @InnerAuth
    @GetMapping("/finallyOne")
    public R<ExtendTargetProgress> finallyOne() {
        return R.ok(extendService.finallyOne());
    }

}
