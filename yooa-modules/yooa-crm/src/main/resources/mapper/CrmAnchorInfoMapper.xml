<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmAnchorInfoMapper">

    <select id="selectAnchorInfoList" resultType="com.yooa.crm.api.domain.vo.AnchorInfoVo">
        WITH operate_user AS (
            SELECT
                u.user_id,
                u.nick_name,
                d.dept_id,
                d.dept_name,
                d.ancestors_names
            FROM yooa_system.sys_user u
            LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
            <!--
             WHERE d.dept_type = '2' AND u.user_type != '1'-->
            WHERE u.user_type != '1'
        )

        SELECT
            DISTINCT
            ai.anchor_id AS anchorId,
            ai.anchor_name AS anchorName,
            ai.sex AS sex,
            ai.language AS language,
            ai.interview_date AS interviewDate,
            ai.cooperation_date AS cooperationDate,
            ai.start_live_date AS startLiveDate,
            ai.channel AS channel,
            ai.anchor_type AS anchorType,
            ai.anchor_style AS anchorStyle,
            ai.anchor_status AS anchorStatus,
            ai.position AS position,
            ii.nick_name AS inviterName,
            fr1.nick_name AS firstRecruiterName,
            fr2.nick_name AS finalRecruiterName,
            ou.user_id AS operate_id,
            ou.nick_name AS operateNickName,
            oud.dept_name AS operateDeptName,
            oud.ancestors_names AS operateDeptAncestorsNames,
            cas.style_name AS anchorStyleName
        FROM crm_anchor_info ai
        <!-- 此关联用于数据权限 -->
        LEFT JOIN operate_user u ON ai.inviter_id = u.user_id
                                        OR ai.first_recruiter_id = u.user_id
                                        OR ai.final_recruiter_id = u.user_id
                                        OR ai.operate_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        <!-- 邀约人 -->
        LEFT JOIN yooa_system.sys_user ii ON ai.inviter_id = ii.user_id
        <!-- 初试人 -->
        LEFT JOIN yooa_system.sys_user fr1 ON ai.first_recruiter_id = fr1.user_id
        <!-- 复试人 -->
        LEFT JOIN yooa_system.sys_user fr2 ON ai.final_recruiter_id = fr2.user_id
        <!-- 运营 -->
        LEFT JOIN yooa_system.sys_user ou ON ai.operate_id = ou.user_id
        LEFT JOIN yooa_system.sys_dept oud ON ou.dept_id = oud.dept_id
        LEFT JOIN crm_anchor_style AS cas ON ai.anchor_style = cas.style_id
        <where>
            <if test="query.anchorName != null">
                and ai.anchor_name like concat('%', #{query.anchorName}, '%')
            </if>
            <if test="query.sex != null and query.sex != ''">
                and ai.sex = #{query.sex}
            </if>
            <if test="query.language != null and query.language != ''">
                and ai.language = #{query.language}
            </if>
            <if test="query.interviewDate != null">
                and (ai.interview_date BETWEEN #{query.interviewDate[0]} AND #{query.interviewDate[1]})
            </if>
            <if test="query.cooperationDate != null">
                and (ai.cooperation_date BETWEEN #{query.cooperationDate[0]} AND #{query.cooperationDate[1]})
            </if>
            <if test="query.channel != null and query.channel != ''">
                and ai.channel = #{query.channel}
            </if>
            <if test="query.anchorType != null and query.anchorType != ''">
                and ai.anchor_type = #{query.anchorType}
            </if>
            <if test="query.anchorStyle != null and query.anchorStyle != ''">
                and ai.anchor_style = #{query.anchorStyle}
            </if>
            <if test="query.anchorStatus != null and query.anchorStatus != ''">
                and ai.anchor_status = #{query.anchorStatus}
            </if>
            <if test="query.operateNickName != null and query.operateNickName != ''">
                and ou.nick_name like concat('%', #{query.operateNickName}, '%')
            </if>
            <if test="query.operateDeptId != null">
                and (oud.dept_id = #{query.operateDeptId} OR  FIND_IN_SET(#{query.operateDeptId}, oud.ancestors_names))
            </if>
            ${query.params.dataScope}
        </where>
        ORDER BY ai.create_time DESC
    </select>

    <select id="selectAnchorInfoById" resultType="com.yooa.crm.api.domain.vo.AnchorInfoVo">
        select
        ai.*,
        ii.nick_name AS inviterName,
        fr1.nick_name AS firstRecruiterName,
        fr2.nick_name AS finalRecruiterName,
        ou.user_id AS operate_id,
        ou.nick_name AS operateNickName,
        oud.dept_name AS operateDeptName,
        oud.ancestors_names AS operateDeptAncestorsNames,
        cas.style_name AS anchorStyleName
        from crm_anchor_info ai
        <!-- 邀约人 -->
        LEFT JOIN yooa_system.sys_user ii ON ai.inviter_id = ii.user_id
        <!-- 初试人 -->
        LEFT JOIN yooa_system.sys_user fr1 ON ai.first_recruiter_id = fr1.user_id
        <!-- 复试人 -->
        LEFT JOIN yooa_system.sys_user fr2 ON ai.final_recruiter_id = fr2.user_id
        <!-- 运营 -->
        LEFT JOIN yooa_system.sys_user ou ON ai.operate_id = ou.user_id
        LEFT JOIN yooa_system.sys_dept oud ON ou.dept_id = oud.dept_id
        LEFT JOIN crm_anchor_style AS cas ON ai.anchor_style = cas.style_id
        WHERE ai.anchor_id = #{anchorId}
    </select>

    <select id="selectAccountListByAnchorId" resultType="com.yooa.crm.api.domain.vo.AnchorAccountMappingVo">
        SELECT
            ai.anchor_id AS anchorId,
            ai.anchor_name AS anchorName,
            a.anchor_id AS accountId,
            a.anchor_name AS accountNickName,
            a.anchor_account AS accountName
        FROM crm_anchor_account_mapping aam
        LEFT JOIN crm_anchor_info ai ON aam.anchor_id = ai.anchor_id
        LEFT JOIN crm_anchor a ON aam.account_id = a.anchor_id
        WHERE aam.anchor_id = #{anchorId}
    </select>


    <select id="getAnchorInfoDetail" resultType="com.yooa.crm.api.domain.vo.AnchorInfoDetailVo">
        SELECT
            a.anchor_id anchorAccountId,
            a.anchor_name anchorName,
            a.create_time registerTime,
            a.anchor_account anchorAccount,
            CASE WHEN DATE(a.today_live_date) = CURDATE() THEN a.today_live_hour  ELSE 0 END todayLiveHour,
            CASE WHEN DATE_FORMAT(a.today_live_date,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') THEN a.month_live_hour  ELSE 0 END monthLiveHor,
            a.total_live_hour totalLiveHor
        FROM crm_anchor_account_mapping aam
                 LEFT JOIN crm_anchor_info ai ON aam.anchor_id = ai.anchor_id
                 LEFT JOIN crm_anchor a ON aam.account_id = a.anchor_id
        WHERE aam.anchor_id = #{anchorId}
    </select>

    <select id="existsBindPyOperateId" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM
            crm_anchor_info ai
                INNER JOIN yooa_system.sys_user_pd up ON ai.operate_id = up.user_id
        WHERE
            ai.operate_id = #{operateId}
    </select>

    <select id="getAnchorRoster" resultType="com.yooa.crm.api.domain.vo.AnchorRosterVo">
        SELECT t.*,
        u.nick_name operateName,
        d.dept_name operateDeptName,
        d.ancestors_names operateAncestorsName
        FROM (
           SELECT
        ai.anchor_id,
        ai.flower_name anchorNickName,
        ai.anchor_name anchorName,
        a.anchor_name pdAnchorName,
        ai.education_type educationType,
        ai.sex,
        ai.anchor_type anchorType,
        ai.anchor_style anchorStyle,
        ai.language,
        ai.anchor_status anchorStatus,
        ai.id_card_number idCardNumber,
        ai.phone,
        ai.create_time createTime,
        ai.cooperation_date cooperationDate,
        ai.stop_live_date stopLiveTime,
        ai.operate_id,
        ai.create_time,
        ROW_NUMBER() OVER(PARTITION BY ai.anchor_id ORDER BY a.anchor_id DESC)  rn
        FROM
        crm_anchor_info ai
        LEFT JOIN crm_anchor_account_mapping aam ON aam.anchor_id = ai.anchor_id
        LEFT JOIN crm_anchor a on a.anchor_id = aam.account_id
        ) t
        LEFT JOIN yooa_system.sys_user u ON t.operate_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d on u.dept_id = d.dept_id
        WHERE t.rn = 1
            ${query.params.dataScope}
        <if test="query.educationType != null ">
            AND t.education_type = #{query.educationType}
        </if>
        <if test="query.sex != null">
            AND t.sex = #{query.sex}
        </if>
        <if test="query.anchorType != null">
            AND t.anchor_type = #{query.anchorType}
        </if>
        <if test="query.language != null">
            AND t.language = #{query.language}
        </if>
        <if test="query.anchorStatus != null">
            AND t.anchor_status = #{query.anchorStatus}
        </if>
        <if test="query.beginTime != null">
            AND t.cooperation_date >= #{query.beginTime}
        </if>
        <if test="query.endTime != null">
            AND t.cooperation_date &lt;= #{query.beginTime}
        </if>
        <if test="query.name != null and query.name != ''">
            AND (t.nike_name LIKE CONCAT('%',#{query.name},'%')
                OR t.flower_name LIKE CONCAT('%',#{query.name},'%')
                OR t.anchor_name LIKE CONCAT('%',#{query.name},'%')
            )
        </if>
        ORDER BY t.create_time
    </select>


</mapper>
