<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmAnchorMapper">

    <update id="updateRewardMoneyByAnchorIds">
        UPDATE crm_anchor
        SET today_reward_money = 0,
            month_reward_money = 0,
            total_reward_money = 0
        WHERE anchor_id IN
        <foreach collection="ids" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectByAccountId" resultType="com.yooa.crm.api.domain.vo.AnchorAccountVo">
        SELECT
            anchor_id AS accountId,
            anchor_name AS accountNickName,
            anchor_account AS accountName
        FROM crm_anchor
        WHERE anchor_id = #{accountId}
    </select>

    <sql id="selectOperate">
        ao.operate_id operateId,
            ai.anchor_id anchorId,
            ai.anchor_name anchorName,
            u.user_id userId,
            u.user_name userName,
            u.nick_name nickName,
            d.dept_id deptId,
            d.dept_name deptName,
            d.ancestors_names ancestorsNames,
            ao.join_time receiveTime,
            ao.lose_time loseTime
    </sql>

    <select id="selectByAnchorAccountIds" resultType="com.yooa.crm.api.domain.CrmAnchor">
        SELECT
            ca.anchor_id anchorId,
            ca.today_live_hour todayLiveLour,
            ca.month_live_hour monthLiveHour,
            ca.total_live_hour totalLiveHour
        FROM crm_anchor ca
        WHERE ca.anchor_id IN
        <foreach item="id" collection="anchorAccountIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="currentOperate" resultType="com.yooa.crm.api.domain.vo.AnchorOperateVo">
        SELECT
        <include refid="selectOperate"/>
        FROM
        crm_anchor_account_mapping aam
                INNER JOIN crm_anchor_info ai ON aam.anchor_id = ai.anchor_id
                INNER JOIN crm_anchor a ON aam.account_id = a.anchor_id AND a.status = 0
                LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = a.anchor_id
                LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = ao.operate_id AND up.type = 2
                LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
                LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
            where
            ai.anchor_id = #{anchorId}
            and ao.lose_time is null
            GROUP BY ai.anchor_id
    </select>

    <select id="recruitOperate" resultType="com.yooa.crm.api.domain.vo.RecruitOperateVo">
        SELECT
            u.user_name recruitName,
            d.dept_name deptName,
            d.ancestors_names ancestorsNames,
            ai.create_time recruitTime,
            CASE
                WHEN d.dept_type THEN
                    2 ELSE 1
                END recruitType
        FROM
            crm_anchor_info ai
                LEFT JOIN yooa_system.sys_user u ON ai.inviter_id = u.user_id
                LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        WHERE
            ai.anchor_id = #{anchorId}
    </select>

    <!--查询历史运营数据 -->
    <select id="getHistoryOperateTeam" resultType="com.yooa.crm.api.domain.vo.AnchorOperateVo">
        SELECT
        <include refid="selectOperate"/>
        FROM
        crm_anchor_account_mapping aam
        INNER JOIN crm_anchor_info ai ON aam.anchor_id = ai.anchor_id
        INNER JOIN crm_anchor a ON aam.account_id = a.anchor_id
        LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = a.anchor_id
        LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = ao.operate_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        where
        ai.anchor_id = #{anchorId}
        and ao.lose_time is not null
    </select>

    <select id="getAnchorDetail" resultType="com.yooa.crm.api.domain.vo.AnchorManageVo">
        SELECT
        td.main_anchor_id anchorId,
        ma.max_account_id pdAnchorId,
        td.total_live_hours totalLiveHour,
        td.total_reward_money totalRewardMoney,
        td.today_live_hour todayLiveHour,
        td.totay_reward_money todayRewardMoney,
        td.month_live_hour monthLiveHour,
        td.month_reward_money monthRewardMoney,
        ma.anchor_name anchorName,
        ma.flower_name nickName,
        ma.sex sex,
        ma.anchor_type anchorType,
        ma.anchor_style anchorStyle,
        ma.language language,
        ma.region region,
        ma.anchor_status anchorStatus,
        ma.start_live_date startTime,
        ma.user_id userId,
        ma.cloud_account cloudAccount,
        ma.base_salary minimumAmt,
        ma.stop_live_date stopLiveTime,
        ma.create_time
        FROM (
        <!-- 子查询1：计算总和 -->
        SELECT
        ai.anchor_id AS main_anchor_id,
        ROUND(SUM(a.total_live_hour),2) AS total_live_hours,
        ROUND(SUM(a.total_reward_money),2) AS total_reward_money,
        IFNULL(SUM(CASE WHEN DATE(a.today_live_date) = CURDATE() THEN a.today_live_hour ELSE 0 END ), 0) today_live_hour,
        IFNULL(SUM(CASE WHEN DATE(a.today_reward_date) = CURDATE() THEN a.today_reward_money ELSE 0 END ), 0) totay_reward_money,
        IFNULL(SUM(CASE WHEN DATE_FORMAT(a.today_live_date,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') THEN a.month_live_hour ELSE 0 END), 0) month_live_hour,
        IFNULL(SUM(CASE WHEN DATE_FORMAT(a.today_reward_date,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') THEN a.month_reward_money ELSE 0 END), 0) month_reward_money
        FROM
        crm_anchor_info ai
        LEFT JOIN crm_anchor_account_mapping aam ON ai.anchor_id = aam.anchor_id
        LEFT JOIN crm_anchor a ON aam.account_id = a.anchor_id
        LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = aam.account_id
        LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = ao.operate_id
        GROUP BY
        ai.anchor_id
        ) td
        LEFT JOIN (
       <!-- 子查询2：获取最大账号的字段 -->
        SELECT
        ai.anchor_id AS main_anchor_id,
        a.anchor_id AS max_account_id,
        ai.flower_name,
        ai.anchor_name,
        ai.sex,
        ai.anchor_type,
        ai.anchor_style,
        ai.language,
        ai.region,
        ai.anchor_status,
        ai.start_live_date,
        ai.operate_id AS user_id,
        ai.cloud_account,
        ai.base_salary,
        ai.stop_live_date,
        ai.create_time
        FROM crm_anchor_info ai
        LEFT JOIN (
        SELECT
        aam.anchor_id,
        MAX(a2.anchor_id) AS max_account_id
        FROM crm_anchor_account_mapping aam
        INNER JOIN crm_anchor a2 ON aam.account_id = a2.anchor_id
        GROUP BY aam.anchor_id
        ) max_acc ON ai.anchor_id = max_acc.anchor_id
        LEFT JOIN crm_anchor a ON max_acc.max_account_id = a.anchor_id
        LEFT JOIN crm_anchor_operate ao ON a.anchor_id = ao.anchor_id
        LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = ao.operate_id
        ) ma ON td.main_anchor_id = ma.main_anchor_id
        <where>
            ma.user_id = #{anchorDetailQuery.operateId}
            <if test="anchorDetailQuery.nameOrAccountQuery != null and anchorDetailQuery.nameOrAccountQuery != ''">
                and (ma.main_anchor_id = #{anchorDetailQuery.nameOrAccountQuery}
                or
                ma.flower_name like concat('%',#{anchorDetailQuery.nameOrAccountQuery},'%')
                or
                ma.anchor_name like concat('%',#{anchorDetailQuery.nameOrAccountQuery},'%'))
            </if>
            <if test="anchorDetailQuery.sex != null">
                and ma.sex = #{anchorDetailQuery.sex}
            </if>
            <if test="anchorDetailQuery.anchorStyle != null">
                and ma.anchor_style = #{anchorDetailQuery.anchorStyle}
            </if>
            <if test="anchorDetailQuery.language != null">
                and ma.LANGUAGE = #{anchorDetailQuery.language}
            </if>
            <if test="anchorDetailQuery.region != null and anchorDetailQuery.region !=''">
                and ma.region = #{anchorDetailQuery.region}
            </if>
            <if test="anchorDetailQuery.anchorStatus != null">
                and ma.anchor_status = #{anchorDetailQuery.anchorStatus}
            </if>
            <if test="anchorDetailQuery.anchorType != null">
                and ma.anchor_type = #{anchorDetailQuery.anchorType}
            </if>
            <if test="anchorDetailQuery.startTime != null">
                and ma.start_live_date >= #{anchorDetailQuery.startTime}
            </if>
            <if test="anchorDetailQuery.endTime != null">
                and ma.start_live_date &lt;= #{anchorDetailQuery.startTime}
            </if>
        </where>
        ORDER BY ma.create_time DESC
    </select>

    <select id="getAnchorJoinFansList" resultType="com.yooa.crm.api.domain.vo.AnchorJoinFansVo">
        SELECT
        cja.customer_id customerId,
        c.customer_name customerName,
        cja.join_time joinTime,
        CASE WHEN cja.join_time BETWEEN DATE_ADD(NOW(),INTERVAL -15 DAY) AND NOW() THEN 0 ELSE 1 END newFansFlag,
        cf.friend_id friendId
        FROM
        crm_anchor_account_mapping aam
        INNER JOIN crm_anchor_info ai ON aam.anchor_id = ai.anchor_id
        INNER JOIN crm_anchor a ON aam.account_id = a.anchor_id
        INNER JOIN crm_customer_join_anchor cja on cja.anchor_id = a.anchor_id
        LEFT JOIN crm_customer c ON cja.customer_id = c.customer_id
        LEFT JOIN crm_customer_friend cf on cf.customer_id = cja.customer_id
        WHERE
        cja.status = '1'
        and aam.anchor_id = #{anchorJoinFansQuery.anchorId}
        <if test="anchorJoinFansQuery.customerIdOrNameQuery != null and anchorJoinFansQuery.customerIdOrNameQuery !=''">
            and ( cja.customer_id = #{anchorJoinFansQuery.customerIdOrNameQuery}
            or c.customer_name like concat ('%', #{anchorJoinFansQuery.customerIdOrNameQuery} ,'%'))
        </if>
        <choose>
            <when test="anchorJoinFansQuery.fansType != null and anchorJoinFansQuery.fansType == 0">
                AND cja.join_time BETWEEN DATE_ADD(NOW(),INTERVAL -15 DAY) AND NOW()
            </when>
            <when test="anchorJoinFansQuery.fansType != null and anchorJoinFansQuery.fansType == 1">
                AND cja.join_time BETWEEN cja.join_time AND DATE_ADD(NOW(),INTERVAL -15 DAY)
            </when>
        </choose>
        GROUP BY cja.customer_id
        ORDER BY cja.join_time DESC
    </select>



    <select id="getRewardAndLiveHour" resultType="com.yooa.crm.api.domain.vo.AnchorDetailVo">
        SELECT
            IFNULL(SUM( a.total_reward_money ),0) totalAllMoney,
            SUM( CASE WHEN DATE_FORMAT(a.today_reward_date,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') THEN a.month_reward_money ELSE 0 END) monthTotalRewardMoney,
            ROUND(SUM( CASE WHEN DATE( a.today_live_date ) = CURDATE() THEN a.today_live_hour ELSE 0 END ), 2) todayTotalLiveHour,
            ROUND(SUM( CASE WHEN DATE( a.today_reward_date ) = CURDATE() THEN a.today_reward_money ELSE 0 END ), 2) todayTotalRewardMoney,
            SUM( CASE WHEN DATE_FORMAT(a.today_live_date,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') THEN a.month_live_hour ELSE 0 END) monthTotalLiveHour,
            IFNULL(ROUND(SUM( a.total_live_hour ), 2), 0) totalAllLiveHour
        FROM
            crm_anchor_operate ao
                LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = ao.operate_id
                LEFT JOIN crm_anchor_account_mapping aam ON ao.anchor_id = aam.account_id
                LEFT JOIN crm_anchor a ON aam.account_id = a.anchor_id
                LEFT JOIN crm_anchor_info ai ON ai.anchor_id = aam.anchor_id
        WHERE
            up.user_id = #{operateId}
    </select>
    <select id="getAnchorReward" resultType="com.yooa.crm.api.domain.vo.AnchorRewardInfoVo">
        SELECT
            ai.anchor_id anchorId,
            cr.customer_id AS customerId,
            c.customer_name AS customerName,
            a.anchor_name AS anchorName,
            a.anchor_id AS anchorAccountId,
            CONCAT(DATE_FORMAT(alr.start_time,'%Y年%m月%d日 %H:%i'),'~', TIME_FORMAT(alr.end_time,'%H:%i')) AS liveHoursDate,
            ROUND((TIMESTAMPDIFF(SECOND, alr.start_time, alr.end_time ) / 3600.0), 2) AS liveHours,
            count(1) rewardNum,
            sum(total_amount) rewardMoney,
            cf.friend_id friendId
        FROM crm_anchor_account_mapping aam
                 INNER JOIN crm_anchor_info ai on aam.anchor_id = ai.anchor_id
                 INNER JOIN crm_anchor a on a.anchor_id = aam.account_id
                 INNER JOIN crm_anchor_live_record alr ON alr.anchor_id = a.anchor_id
                 INNER JOIN crm_customer_reward cr ON a.anchor_id = cr.anchor_id
                               AND add_time BETWEEN alr.start_time AND alr.end_time
                 LEFT JOIN crm_customer c ON cr.customer_id = c.customer_id
                 LEFT JOIN (
                         SELECT customer_id, view_time enteringTime, anchor_id FROM crm_customer_view_live_time GROUP BY anchor_id,customer_id,view_date) cvlt
                               ON cvlt.customer_id = cr.customer_id
                               AND cvlt.anchor_id = alr.anchor_id
                               AND cvlt.enteringTime BETWEEN alr.start_time AND alr.end_time
                 LEFT JOIN (
                         SELECT customer_id,friend_id,ROW_NUMBER() OVER( PARTITION BY customer_id ORDER BY begin_time DESC) rn
                              FROM crm_customer_friend
                      ) cf on cf.customer_id = cr.customer_id AND cf.rn = 1
        WHERE aam.anchor_id = #{anchorRewardQuery.anchorId}
        <if test="anchorRewardQuery.startTime != null and anchorRewardQuery.endTime != null ">
            AND alr.start_time BETWEEN #{anchorRewardQuery.startTime} AND DATE_ADD(#{anchorRewardQuery.endTime},INTERVAL 1 DAY) - INTERVAL 1 SECOND
        </if>
        <if test="anchorRewardQuery.nameOrIdQuery != null and anchorRewardQuery.nameOrIdQuery != ''">
            AND (cr.customer_id = #{anchorRewardQuery.nameOrIdQuery}
            OR  c.customer_name like CONCAT('%',#{anchorRewardQuery.nameOrIdQuery},'%'))
        </if>
        <if test="anchorRewardQuery.anchorAccountId != null">
            AND a.anchor_id = #{anchorRewardQuery.anchorAccountId}
        </if>
        GROUP BY a.anchor_id, DATE(alr.start_time), cr.customer_id
        HAVING 1 = 1
        <if test="anchorRewardQuery.minRewardNum != null and anchorRewardQuery.minRewardNum != ''
             and anchorRewardQuery.maxRewardNum !=null and anchorRewardQuery.maxRewardNum != '' ">
            AND  rewardNum &gt;= #{anchorRewardQuery.minRewardNum} AND rewardNum &lt;= #{anchorRewardQuery.maxRewardNum}
        </if>
        <if test="anchorRewardQuery.minRewardAmount != null and anchorRewardQuery.maxRewardAmount != null ">
            AND rewardMoney &gt;= #{anchorRewardQuery.minRewardAmount} AND rewardMoney &lt;= #{anchorRewardQuery.maxRewardAmount}
        </if>
        ORDER BY alr.start_time DESC
    </select>
    <!-- -->
    <select id="getAnchorRewardCollect" resultType="com.yooa.crm.api.domain.vo.AnchorRewardCollectVo">
        SELECT
            a.anchor_id anchorId,
            IFNULL(SUM(CASE WHEN DATE( a.today_reward_date ) = CURDATE() THEN a.today_reward_money END),0) todayRewardAmt,
            IFNULL(SUM(CASE WHEN DATE_FORMAT(a.today_live_date,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') THEN a.month_reward_money END),0) monthRewardAmt,
            IFNULL(SUM(a.total_reward_money),0) totalRewardAmt
        FROM
            crm_anchor_account_mapping aam
                INNER JOIN crm_anchor_info ai ON aam.anchor_id = ai.anchor_id
                INNER JOIN crm_anchor a ON a.anchor_id = aam.account_id
                LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = a.anchor_id
                LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = ao.operate_id
        WHERE 1 = 1
          <choose>
              <when test="query.anchorAccountId !=null and query.anchorAccountId != ''">
                  AND a.anchor_id = #{query.anchorAccountId}
              </when>
              <otherwise>
                  AND ai.anchor_id = #{query.anchorId}
              </otherwise>
          </choose>
        GROUP BY
            ai.anchor_id
    </select>

    <select id="getAnchorList" resultType="com.yooa.crm.api.domain.vo.AnchorVo">
        SELECT
        td.main_anchor_id anchorId,
        ma.max_account_id pdAnchorId,
        ma.anchor_name anchorName,
        ma.flower_name anchorNickName,
        ma.sex sex,
        ma.LANGUAGE LANGUAGE,
        ma.anchor_type anchorType,
        ma.live_role liveRole,
        ma.live_status userStatus,
        ma.base_salary minimumAmt,
        ma.cloud_account cloudAccount,
        ma.dept_name deptName,
        ma.ancestors_names ancestorsNames,
        ma.user_name operationName,
        ma.ancestors ancestors,
        ma.dept_id deptId,
        ma.region region,
        ma.user_id operateId,
        ma.anchor_style anchorStyle,
        td.fansNum joinFans,
        td.reward,
        td.joinFansRate,
        td.firstCharge,
        td.firstChargeRate,
        td.liveStatus,
        td.totalReward,
        td.liveHours,
        td.rewardRate,
        tf.*
        FROM (
        <!-- 子查询1 : 计算主播所有账号累加数据和 -->
        SELECT
        ai.anchor_id AS main_anchor_id,
        cja.joinFansNum AS joinFans,
        ROUND(COALESCE(cja.total_amount, 0), 2) AS reward,
        ar.total_reward totalReward,
        ar.total_live_hours liveHours,
        cja.fans_count AS fansNum,
        ROUND(cja.joinFansNum / NULLIF(cja.fans_count, 0) * 100, 2) AS joinFansRate,
        cja.first_charge_count AS firstCharge,
        ROUND(cja.first_charge_count / NULLIF(cja.fans_count, 0) * 100, 2) AS firstChargeRate,
        CASE
        WHEN ar.is_live_ing = 1 THEN 1
        WHEN DATE(alr.start_time) = CURDATE() THEN 3
        ELSE 2
        END AS liveStatus,
        ROUND(COALESCE(cja.reward_users_total, 0) / NULLIF(cja.fans_count, 0) * 100, 2) AS rewardRate
        FROM crm_anchor_account_mapping aam
        INNER JOIN crm_anchor_info ai ON aam.anchor_id = ai.anchor_id
        INNER JOIN (
        SELECT
        aam.anchor_id,
        SUM(a.total_reward_money) total_reward,
        ROUND(SUM(a.total_live_hour), 2) total_live_hours,
        MAX(a.is_live_ing) is_live_ing
        FROM crm_anchor_account_mapping aam
        INNER JOIN crm_anchor a ON aam.account_id = a.anchor_id
        <if test = "query.liveStartTime != null and query.liveEndTime != null" >
            INNER JOIN (
            SELECT anchor_id
            FROM crm_anchor_live_record
            WHERE start_time BETWEEN #{query.liveStartTime} AND #{query.liveEndTime}
            GROUP BY anchor_id
            ) calr ON a.anchor_id = calr.anchor_id
        </if>
        GROUP BY aam.anchor_id
        ) ar ON ai.anchor_id = ar.anchor_id
        LEFT JOIN (
        SELECT
        aam.anchor_id AS main_anchor_id,
        SUM(cja.join_amt) AS total_amount,
        COUNT(CASE WHEN cja.STATUS = 1 AND cja.join_amt > 0 THEN 1 END) AS joinFansNum,
        COUNT(CASE WHEN cja.STATUS = 1 THEN 1 END) AS fans_count,
        COUNT(CASE WHEN cja.first_charge_date IS NOT NULL THEN 1 END) AS first_charge_count,
        COUNT(DISTINCT CASE WHEN cja.join_amt > 0 THEN cja.customer_id END) AS reward_users_total
        FROM crm_anchor_account_mapping aam
        LEFT JOIN (
        SELECT
        cja.*,
        ao.operate_id AS current_operate_id
        FROM
        ( SELECT *, ROW_NUMBER() OVER ( PARTITION BY anchor_id, customer_id ORDER BY receive_time DESC ) rn FROM crm_customer_join_anchor WHERE STATUS = 1 ) cja
        INNER JOIN crm_anchor_operate ao ON cja.anchor_id = ao.anchor_id
        WHERE
        cja.rn = 1
        AND cja.operate_id = ao.operate_id
        ) cja ON aam.account_id = cja.anchor_id
        GROUP BY
        aam.anchor_id
        ) cja ON cja.main_anchor_id = ai.anchor_id
        LEFT JOIN (
        SELECT anchor_id, MAX(start_time) AS start_time
        FROM crm_anchor_live_record
        GROUP BY anchor_id
        ) alr ON alr.anchor_id IN (
        SELECT account_id
        FROM crm_anchor_account_mapping
        WHERE anchor_id = ai.anchor_id
        )
        LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = aam.account_id
        LEFT JOIN yooa_system.sys_user u ON u.user_id = ai.operate_id
        LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id
        WHERE 1 = 1
        ${query.params.dataScope}
        GROUP BY
        ai.anchor_id
        ) td
        <!-- 子查询2 : 查询主播旗下账号最大一条数据 -->
        LEFT JOIN (
        SELECT * FROM (
        SELECT
        ai.anchor_id main_anchor_id,
        a.anchor_id max_account_id,
        ai.flower_name,
        ai.anchor_name,
        ai.sex,
        ai.anchor_type,
        ai.anchor_style,
        ai.LANGUAGE,
        a.live_role,
        ai.live_status,
        ai.base_salary,
        ai.cloud_account,
        d.dept_name,
        d.ancestors_names,
        u.user_name,
        d.dept_id,
        d.ancestors,
        u.user_id,
        ai.region,
        ROW_NUMBER() OVER(PARTITION BY ai.anchor_id ORDER BY a.anchor_id DESC) AS rn
        FROM crm_anchor_account_mapping aam
        INNER JOIN crm_anchor_info ai ON aam.anchor_id = ai.anchor_id
        INNER JOIN crm_anchor a ON aam.account_id = a.anchor_id
        LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = a.anchor_id
        LEFT JOIN yooa_system.sys_user u ON u.user_id = ai.operate_id
        LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id
        ) t WHERE rn = 1
        ) ma ON td.main_anchor_id = ma.main_anchor_id
        <!-- 子查询3 : 查询主播所有账号粉丝达成累加和 -->
        LEFT JOIN (
        SELECT
        a.anchor_id,
        COUNT(CASE WHEN v.fans_type = 0 THEN 1 END) AS fans1h,
        COUNT(CASE WHEN v.fans_type = 1 THEN 1 END) AS fans2h,
        COUNT(CASE WHEN v.fans_type = 2 THEN 1 END) AS fans5h,
        COUNT(CASE WHEN v.fans_type = 3 THEN 1 END) AS fans5k,
        COUNT(CASE WHEN v.fans_type = 4 THEN 1 END) AS fans5w,
        COUNT(CASE WHEN v.fans_type = 5 THEN 1 END) AS fans10w,
        COUNT(CASE WHEN v.fans_type = 6 THEN 1 END) AS firstCharge
        FROM yooa_extend.operate_vermicelli v
        INNER JOIN (
        SELECT DISTINCT account_id AS pd_anchor_id, anchor_id
        FROM crm_anchor_account_mapping
        ) a ON a.pd_anchor_id = v.anchor_id
        GROUP BY a.anchor_id
        ) tf ON tf.anchor_id = td.main_anchor_id
        <where>
            1 = 1
            <if test="query.deptId != null ">
                AND (FIND_IN_SET(#{query.deptId},ma.ancestors) OR ma.dept_id = #{query.deptId})
            </if>
            <if test="query.language != null and query.language.size() > 0">
                AND ma.language IN
                (
                <foreach collection="query.language" item="item" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="query.anchorType != null and query.anchorType.size() > 0">
                AND ma.anchor_type IN (
                <foreach collection="query.anchorType" item="item" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="query.region != null and query.region.size() > 0 ">
                AND ma.region IN (
                <foreach collection="query.region" item="item" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="query.status != null and query.status.size() >0">
                AND ma.live_status IN (
                <foreach collection="query.status" item="item" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="query.anchorStyle != null and query.anchorStyle.size() >0">
                AND ma.anchor_style IN (
                <foreach collection="query.anchorStyle" item="item" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="query.anchorIdOrNameQuery != null and query.anchorIdOrNameQuery != '' ">
                AND (ma.anchor_name LIKE CONCAT('%',#{query.anchorIdOrNameQuery},'%')
                OR ma.flower_name LIKE CONCAT('%',#{query.anchorIdOrNameQuery},'%')
                OR ma.max_account_id = #{query.anchorIdOrNameQuery})
            </if>
            <if test="query.sex != null and query.sex.size() >0">
                AND ma.sex IN (
                <foreach collection="query.sex" item="item" separator=",">
                    #{item}
                </foreach>)
            </if>
        </where>
        <choose>
            <when test="query.filed != null and query.filed != '' ">
                ORDER BY ${query.filed}
                <choose>
                    <when test="query.order != null and query.order != ''">
                        ${query.order}
                    </when>
                    <otherwise>
                        DESC
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                ORDER BY td.totalReward DESC
            </otherwise>
        </choose>
    </select>
    <select id="getAnchorCollect" resultType="com.yooa.crm.api.domain.vo.AnchorCollectVo">
        select
            SUM(a.total_reward_money) totalRewardAmt,
            SUM(CASE WHEN DATE( a.today_reward_date ) = CURDATE() THEN a.today_reward_money ELSE 0 END) todayRewardAmt,
            SUM(CASE WHEN DATE_FORMAT(a.today_reward_date,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') THEN a.month_reward_money ELSE 0 END) monthRewardAmt,
            ROUND(SUM(a.total_live_hour),2) totalLiveHours,
            ROUND(SUM(CASE WHEN DATE( a.today_live_date ) = CURDATE() THEN a.today_live_hour ELSE 0 END),2) todayLiveHours,
            ROUND(SUM(CASE WHEN DATE_FORMAT(a.today_live_date,'%Y-%m') = DATE_FORMAT(NOW(),'%Y-%m') THEN a.month_live_hour ELSE 0 END),2) monthLiveHours
        from
            crm_anchor_account_mapping aam
                INNER JOIN crm_anchor_info ai ON ai.anchor_id = aam.anchor_id
                LEFT JOIN crm_anchor a ON aam.account_id = a.anchor_id
                LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = a.anchor_id
                LEFT JOIN yooa_system.sys_user u ON u.user_id = ai.operate_id
                LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id
            where 1 = 1
            ${query.params.dataScope}
    </select>

    <select id="getCustomerRewardAmt" resultType="com.yooa.crm.api.domain.vo.AnchorJoinFansVo">
        SELECT
        cr.customer_id,
        SUM( CASE
        WHEN  DATE_FORMAT(cr.add_time,'%Y-%m') >= DATE_FORMAT(NOW(), '%Y-%m')
        AND  DATE_FORMAT(cr.add_time,'%Y-%m') &lt; DATE_FORMAT(NOW() + INTERVAL 1 MONTH, '%Y-%m')
        THEN total_amount
        ELSE 0
        END ) AS month_amt,
        SUM(cr.total_amount) AS total_amt
        FROM
        crm_customer_reward cr
        LEFT JOIN crm_anchor_account_mapping aam on cr.anchor_id = aam.account_id
        LEFT JOIN crm_anchor a on a.anchor_id = aam.account_id
        LEFT JOIN crm_anchor_info ai on ai.anchor_id = aam.anchor_id
        WHERE ai.anchor_id = #{anchorId}
        AND cr.customer_id in
        <foreach collection="ids" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
        customer_id
    </select>
    <select id="getAnchorIds" resultType="com.yooa.crm.api.domain.vo.AnchorVo">
        SELECT a.anchor_id anchorId,a.anchor_name anchorName, ai.anchor_id pdAnchorId FROM crm_anchor_account_mapping aam
                           INNER JOIN crm_anchor_info ai on aam.anchor_id = ai.anchor_id
                           INNER JOIN crm_anchor a on a.anchor_id = aam.account_id
        WHERE ai.anchor_id = #{anchorId}
    </select>

    <select id="getRewardDetail" resultType="com.yooa.crm.api.domain.vo.AnchorRewardDetailVo">
        SELECT
        cr.customer_id  customerId,
        cr.total_amount rewardMoney,
        cr.anchor_id anchorId,
        cr.add_time addTime,
        cf.friend_id friendId,
        cr.action,
        cr.gift_id giftId
        FROM
        crm_customer_reward cr
        <if test="anchorRewardQuery.anchorAccountId != null">
            LEFT JOIN crm_anchor a on a.anchor_id = cr.anchor_id
        </if>
        <if test="anchorRewardQuery.queryId != null and anchorRewardQuery.queryId != ''">
            LEFT JOIN crm_customer c on c.customer_id = cr.customer_id
        </if>
        LEFT JOIN ( SELECT customer_id, friend_id, ROW_NUMBER() OVER ( PARTITION BY customer_id ORDER BY begin_time DESC ) rn FROM crm_customer_friend ) cf ON cf.customer_id = cr.customer_id
        AND cf.rn = 1
        where cr.anchor_id in
        <foreach collection="ids" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
        <if test="anchorRewardQuery.startTime != null and anchorRewardQuery.endTime != null ">
            AND cr.add_time BETWEEN #{anchorRewardQuery.startTime} AND DATE_ADD(#{anchorRewardQuery.endTime},INTERVAL 1 DAY) - INTERVAL 1 SECOND
        </if>
        <if test="anchorRewardQuery.queryId != null and anchorRewardQuery.queryId != ''">
            AND (cr.customer_id = #{anchorRewardQuery.queryId}
            OR  c.customer_name like CONCAT('%',#{anchorRewardQuery.queryId},'%'))
        </if>
        <if test="anchorRewardQuery.anchorAccountId != null">
            AND a.anchor_id = #{anchorRewardQuery.anchorAccountId}
        </if>
        <if test="anchorRewardQuery.minRewardAmount != null">
            AND cr.total_amount &gt;= #{anchorRewardQuery.minRewardAmount}
        </if>
        <if test="anchorRewardQuery.maxRewardAmount != null ">
            AND cr.total_amount &lt;= #{anchorRewardQuery.maxRewardAmount}
        </if>
        ORDER BY cr.add_time  DESC
    </select>

    <select id="getAnchorLiveRecord" resultType="com.yooa.crm.api.domain.vo.AnchorLiveDetailVo">
        SELECT
            alr.start_time startTime,
            alr.end_time endTime,
            alr.anchor_id,
            CONCAT(
                    DATE_FORMAT( alr.start_time, '%Y年%m月%d日 %H:%i' ),
                    '~',
                    TIME_FORMAT( alr.end_time, '%H:%i' )) AS liveHoursDate,
            ROUND(( TIMESTAMPDIFF( SECOND, alr.start_time, alr.end_time ) / 3600.0 ), 2 ) AS liveHours
        FROM
            crm_anchor_live_record alr
        WHERE
            alr.anchor_id IN
        <foreach collection="ids" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
        GROUP BY DATE(alr.start_time)
    </select>


    <select id="getAnchorLiveRecordByAnchorId" resultType="com.yooa.crm.api.domain.vo.AnchorLiveDetailVo">
        SELECT
        alr.start_time startTime,
        alr.end_time endTime,
        alr.anchor_id,
        CONCAT(
        DATE_FORMAT( alr.start_time, '%Y年%m月%d日 %H:%i' ),
        '~',
        TIME_FORMAT( alr.end_time, '%H:%i' )) AS liveHoursDate,
        ROUND(( TIMESTAMPDIFF( SECOND, alr.start_time, alr.end_time ) / 3600.0 ), 2 ) AS liveHours
        FROM
        crm_anchor_live_record alr
        WHERE
        alr.anchor_id = #{anchorId}
        AND #{addTime} BETWEEN alr.start_time AND alr.end_time
        GROUP BY DATE(alr.start_time)
    </select>
    <select id="getCount" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM
        crm_customer_reward cr
        <if test="anchorRewardQuery.anchorAccountId != null">
            LEFT JOIN crm_anchor a on a.anchor_id = cr.anchor_id
        </if>
        <if test="anchorRewardQuery.queryId != null and anchorRewardQuery.queryId != ''">
            LEFT JOIN crm_customer c on c.customer_id = cr.customer_id
        </if>
        LEFT JOIN ( SELECT customer_id, friend_id, ROW_NUMBER() OVER ( PARTITION BY customer_id ORDER BY begin_time DESC ) rn FROM crm_customer_friend ) cf ON cf.customer_id = cr.customer_id
        AND cf.rn = 1
        where cr.anchor_id in
        <foreach collection="ids" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
        <if test="anchorRewardQuery.startTime != null and anchorRewardQuery.endTime != null ">
            AND cr.add_time BETWEEN #{anchorRewardQuery.startTime} AND DATE_ADD(#{anchorRewardQuery.endTime},INTERVAL 1 DAY) - INTERVAL 1 SECOND
        </if>
        <if test="anchorRewardQuery.queryId != null and anchorRewardQuery.queryId != ''">
            AND (cr.customer_id = #{anchorRewardQuery.queryId}
            OR  c.customer_name like CONCAT('%',#{anchorRewardQuery.queryId},'%'))
        </if>
        <if test="anchorRewardQuery.anchorAccountId != null">
            AND a.anchor_id = #{anchorRewardQuery.anchorAccountId}
        </if>
        <if test="anchorRewardQuery.minRewardAmount != null">
            AND cr.total_amount &gt;= #{anchorRewardQuery.minRewardAmount}
        </if>
        <if test="anchorRewardQuery.maxRewardAmount != null ">
            AND cr.total_amount &lt;= #{anchorRewardQuery.maxRewardAmount}
        </if>
    </select>
    <select id="getRewardDetailPage" resultType="com.yooa.crm.api.domain.vo.AnchorRewardDetailVo">
        SELECT
        cr.customer_id  customerId,
        cr.total_amount rewardMoney,
        cr.anchor_id anchorId,
        cr.add_time addTime,
        cf.friend_id friendId,
        cr.action,
        cr.gift_id giftId
        FROM
        crm_customer_reward cr
        <if test="anchorRewardQuery.anchorAccountId != null">
            LEFT JOIN crm_anchor a on a.anchor_id = cr.anchor_id
        </if>
        <if test="anchorRewardQuery.queryId != null and anchorRewardQuery.queryId != ''">
            LEFT JOIN crm_customer c on c.customer_id = cr.customer_id
        </if>
        INNER JOIN crm_anchor_operate ao on ao.operate_id = cr.operate_id and ao.anchor_id = cr.anchor_id
        LEFT JOIN ( SELECT customer_id, friend_id, ROW_NUMBER() OVER ( PARTITION BY customer_id ORDER BY begin_time DESC ) rn FROM crm_customer_friend ) cf ON cf.customer_id = cr.customer_id
        AND cf.rn = 1
        where cr.anchor_id in
        <foreach collection="ids" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
        <if test="anchorRewardQuery.startTime != null and anchorRewardQuery.endTime != null ">
            AND cr.add_time BETWEEN #{anchorRewardQuery.startTime} AND DATE_ADD(#{anchorRewardQuery.endTime},INTERVAL 1 DAY) - INTERVAL 1 SECOND
        </if>
        <if test="anchorRewardQuery.queryId != null and anchorRewardQuery.queryId != ''">
            AND (cr.customer_id = #{anchorRewardQuery.queryId}
            OR  c.customer_name like CONCAT('%',#{anchorRewardQuery.queryId},'%'))
        </if>
        <if test="anchorRewardQuery.anchorAccountId != null">
            AND a.anchor_id = #{anchorRewardQuery.anchorAccountId}
        </if>
        <if test="anchorRewardQuery.minRewardAmount != null">
            AND cr.total_amount &gt;= #{anchorRewardQuery.minRewardAmount}
        </if>
        <if test="anchorRewardQuery.maxRewardAmount != null ">
            AND cr.total_amount &lt;= #{anchorRewardQuery.maxRewardAmount}
        </if>
        ORDER BY cr.add_time
        LIMIT #{offset}, #{count}
    </select>
    <select id="getAnchorIdsByName" resultType="java.lang.Long">
        SELECT anchor_id FROM crm_anchor WHERE anchor_name LIKE CONCAT('%', #{queryId}, '%')
    </select>
    <select id="getAnchorIdList" resultType="java.lang.Long">
        select aam.anchor_id from crm_anchor_account_mapping aam
        INNER JOIN crm_anchor_info ai ON ai.anchor_id = aam.anchor_id
        INNER JOIN crm_anchor a ON a.anchor_id = aam.account_id
        LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = a.anchor_id
        LEFT JOIN yooa_system.sys_user u ON u.user_id = ai.operate_id
        LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id
        <if test = "query.liveStartTime != null and query.liveEndTime != null" >
            INNER JOIN (
            SELECT anchor_id
            FROM crm_anchor_live_record
            WHERE start_time BETWEEN #{query.liveStartTime} AND #{query.liveEndTime}
            GROUP BY anchor_id
            ) calr ON a.anchor_id = calr.anchor_id
        </if>
        <where>
            1 = 1
            ${query.params.dataScope}
            <if test="query.deptId != null ">
                AND (FIND_IN_SET(#{query.deptId},d.ancestors) OR d.dept_id = #{query.deptId})
            </if>
            <if test="query.language != null and query.language.size() > 0">
                AND ai.language IN (
                <foreach collection="query.language" item="item" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="query.anchorType != null and query.anchorType.size() > 0">
                AND ai.anchor_type IN (
                <foreach collection="query.anchorType" item="item" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="query.region != null and query.region.size() > 0">
                AND ai.region IN (
                <foreach collection="query.region" item="item" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="query.status != null and query.status.size() > 0">
                AND ai.live_status IN (
                <foreach collection="query.status" item="item" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="query.anchorStyle != null and query.anchorStyle.size() > 0">
                AND ai.anchor_style IN (
                <foreach collection="query.anchorStyle" item="item" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="query.anchorIdOrNameQuery != null and query.anchorIdOrNameQuery != '' ">
                AND (ai.anchor_name LIKE CONCAT('%',#{query.anchorIdOrNameQuery},'%')
                OR ai.flower_name LIKE CONCAT('%',#{query.anchorIdOrNameQuery},'%')
                OR a.anchor_id = #{query.anchorIdOrNameQuery})
            </if>
            <if test="query.sex != null and query.sex.size() > 0">
                AND ai.sex IN (
                <foreach collection="query.sex" item="item" separator=",">
                    #{item}
                </foreach>)
            </if>
        GROUP BY ai.anchor_id
        </where>
    </select>
    <select id="getAnchorData" resultType="com.yooa.crm.api.domain.vo.AnchorDataVo">
        SELECT
        ai.anchor_id AS anchorId,
        cja.joinFansNum,
        ROUND(COALESCE(cja.total_amount, 0), 2) AS reward,
        ar.total_reward totalReward,
        ar.total_live_hours liveHours,
        cja.fans_count AS joinFans,
        ROUND(cja.joinFansNum / NULLIF(cja.fans_count, 0) * 100, 4) AS joinFansRate,
        cja.first_charge_count AS firstCharge,
        ROUND(cja.first_charge_count / NULLIF(cja.fans_count, 0) * 100, 4) AS firstChargeRate,
        CASE
        WHEN ar.is_live_ing = 1 THEN 1
        WHEN DATE(alr.start_time) = CURDATE() THEN 3
        ELSE 2
        END AS liveStatus,
        ROUND(COALESCE(cja.reward_users_total, 0) / NULLIF(cja.fans_count, 0) * 100, 4) AS rewardRate
        FROM crm_anchor_account_mapping aam
        INNER JOIN crm_anchor_info ai ON aam.anchor_id = ai.anchor_id
        INNER JOIN (
        SELECT
        aam.anchor_id,
        SUM(a.total_reward_money) total_reward,
        ROUND(SUM(a.total_live_hour), 2) total_live_hours,
        MAX(a.is_live_ing) is_live_ing
        FROM crm_anchor_account_mapping aam
        INNER JOIN crm_anchor a ON aam.account_id = a.anchor_id
        GROUP BY aam.anchor_id
        ) ar ON ai.anchor_id = ar.anchor_id
        LEFT JOIN (
        SELECT
        aam.anchor_id AS main_anchor_id,
        SUM(cja.join_amt) AS total_amount,
        COUNT(CASE WHEN cja.STATUS = 1 AND cja.join_amt > 0 THEN 1 END) AS joinFansNum,
        COUNT(CASE WHEN cja.STATUS = 1 THEN 1 END) AS fans_count,
        COUNT(CASE WHEN cja.first_charge_date IS NOT NULL THEN 1 END) AS first_charge_count,
        COUNT(DISTINCT CASE WHEN cja.join_amt > 0 THEN cja.customer_id END) AS reward_users_total
        FROM crm_anchor_account_mapping aam
        LEFT JOIN (
        SELECT customer_id, anchor_id, join_amt, status, first_charge_date
        FROM (
        SELECT *, ROW_NUMBER() OVER(PARTITION BY anchor_id, customer_id, operate_id ORDER BY receive_time DESC) rn
        FROM crm_customer_join_anchor
        ) t
        WHERE t.rn = 1 AND t.status = 1
        ) cja ON aam.account_id = cja.anchor_id
        GROUP BY aam.anchor_id
        ) cja ON cja.main_anchor_id = ai.anchor_id
        LEFT JOIN (
        SELECT anchor_id, MAX(start_time) AS start_time
        FROM crm_anchor_live_record
        GROUP BY anchor_id
        ) alr ON alr.anchor_id IN (
        SELECT account_id
        FROM crm_anchor_account_mapping
        WHERE anchor_id = ai.anchor_id
        )
        WHERE aam.anchor_id IN
        <foreach collection="ids" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
        ai.anchor_id
    </select>
    <select id="getMaxAnchorData" resultType="com.yooa.crm.api.domain.vo.MaxAnchorDataVo">
        SELECT * FROM (
                          SELECT
                              ai.anchor_id anchorId,
                              a.anchor_id pdAnchorId,
                              a.anchor_name pdAnchorName,
                              ai.flower_name anchorNickName,
                              ai.anchor_name anchorName,
                              ai.sex,
                              ai.anchor_type anchorType,
                              ai.anchor_style anchorStyle,
                              ai.LANGUAGE,
                              a.live_role liveRole,
                              ai.live_status userStatus,
                              ai.base_salary minimumAmt,
                              ai.cloud_account cloudAccount,
                              d.dept_name deptName,
                              d.ancestors_names ancestorsNames,
                              u.nick_name operationName,
                              d.dept_id deptId,
                              d.ancestors,
                              u.user_id userId,
                              ai.region,
                              ROW_NUMBER() OVER(PARTITION BY ai.anchor_id ORDER BY a.anchor_id DESC) AS rn
                          FROM crm_anchor_account_mapping aam
                                   INNER JOIN crm_anchor_info ai ON aam.anchor_id = ai.anchor_id
                                   INNER JOIN crm_anchor a ON aam.account_id = a.anchor_id
                                   LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = a.anchor_id
                                   LEFT JOIN yooa_system.sys_user u ON u.user_id = ai.operate_id
                                   LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id
                      ) t WHERE rn = 1 AND t.anchorId IN
        <foreach collection="ids" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>


    </select>
    <select id="getFansCollect" resultType="com.yooa.crm.api.domain.vo.FansCollectVo">
        SELECT
            a.anchor_id anchorId,
            COUNT(CASE WHEN v.fans_type = 0 THEN 1 END) AS fans1h,
            COUNT(CASE WHEN v.fans_type = 1 THEN 1 END) AS fans2h,
            COUNT(CASE WHEN v.fans_type = 2 THEN 1 END) AS fans5h,
            COUNT(CASE WHEN v.fans_type = 3 THEN 1 END) AS fans5k,
            COUNT(CASE WHEN v.fans_type = 4 THEN 1 END) AS fans5w,
            COUNT(CASE WHEN v.fans_type = 5 THEN 1 END) AS fans10w,
            COUNT(CASE WHEN v.fans_type = 6 THEN 1 END) AS firstCharge
        FROM yooa_extend.operate_vermicelli v
                 INNER JOIN (
            SELECT DISTINCT account_id AS pd_anchor_id, anchor_id
            FROM crm_anchor_account_mapping
        ) a ON a.pd_anchor_id = v.anchor_id
        WHERE a.anchor_id IN
        <foreach collection="ids" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
        GROUP BY a.anchor_id
    </select>

    <select id="getMyAnchorCollect" resultType="com.yooa.crm.api.domain.vo.MyAnchorCollectVo">
        SELECT
            sum(case when ai.live_status = 0 then 1 end)beginToShow,
            sum(case when ai.live_status = 1 then 1  end)discontinueBroadcasting,
            sum(case when ai.live_status = 2 then 1  end)offAirBroadcast,
            sum(case when ai.anchor_status = 0 then 1  end)pendingInterview,
            sum(case when ai.anchor_status = 1 then 1  end)interview,
            sum(case when ai.anchor_status = 3 then 1  end)pendingRetest,
            sum(case when ai.anchor_status = 4 then 1  end)inTheSecondInterview,
            sum(case when ai.anchor_status = 6 then 1  end)toCooperate,
            sum(case when ai.anchor_status = 7 then 1  end)collaboratedAlready,
            sum(case when ai.anchor_status = 8 then 1  end)cancelCooperation,
            sum(case when ai.anchor_status = 2 OR ai.anchor_status = 5 then 1  end)fail
        FROM
            crm_anchor_info ai
        where
            ai.operate_id = #{operateId}
    </select>
    <select id="getSumFansCollect" resultType="com.yooa.crm.api.domain.vo.AnchorSumFansCollectVo">
        SELECT
            IFNULL(SUM( t.fans1h ),0)fans1h,
            IFNULL(SUM( t.fans2h ),0)fans2h,
            IFNULL(SUM( t.fans5h ),0)fans5h,
            IFNULL(SUM( t.fans5k ),0)fans5k,
            IFNULL(SUM( t.fans10w ),0)fans5w,
            IFNULL(SUM( t.firstCharge),0) firstCharge
        FROM (SELECT a.anchor_id                                    anchorId,
                     COUNT(CASE WHEN v.fans_type = 0 THEN 1 END) AS fans1h,
                     COUNT(CASE WHEN v.fans_type = 1 THEN 1 END) AS fans2h,
                     COUNT(CASE WHEN v.fans_type = 2 THEN 1 END) AS fans5h,
                     COUNT(CASE WHEN v.fans_type = 3 THEN 1 END) AS fans5k,
                     COUNT(CASE WHEN v.fans_type = 4 THEN 1 END) AS fans5w,
                     COUNT(CASE WHEN v.fans_type = 5 THEN 1 END) AS fans10w,
                     COUNT(CASE WHEN v.fans_type = 6 THEN 1 END) AS firstCharge
              FROM yooa_extend.operate_vermicelli v
                       INNER JOIN (SELECT DISTINCT account_id AS pd_anchor_id, anchor_id
                                   FROM crm_anchor_account_mapping) a ON a.pd_anchor_id = v.anchor_id
                       LEFT JOIN crm_anchor_operate ao on ao.anchor_id = a.pd_anchor_id
                       LEFT JOIN yooa_system.sys_user_pd up on ao.operate_id = up.pd_user_id
                       LEFT JOIN yooa_system.sys_user u on up.user_id = u.user_id
                       LEFT JOIN yooa_system.sys_dept d on d.dept_id = u.dept_id
              WHERE 1 = 1
                  ${query.params.dataScope}
              GROUP BY a.anchor_id
              ) t

    </select>

    <select id="getSumAnchorDataCollect" resultType="com.yooa.crm.api.domain.vo.AnchorSumDataCollectVo">
        SELECT
            SUM(t.joinFansNum) AS totalJoinFansNum,
            ROUND(SUM(t.reward), 2) AS totalReward,
            ROUND(SUM(t.totalReward), 2) AS totalRewardMoney,
            ROUND(SUM(t.liveHours), 2) AS totalLiveHours,
            SUM(t.joinFans) AS totalFans,
            ROUND(SUM(t.joinFansNum) / NULLIF(SUM(t.joinFans), 0) * 100, 4) AS totalJoinFansRate,
            SUM(t.firstCharge) AS totalFirstCharge,
            ROUND(SUM(t.firstCharge) / NULLIF(SUM(t.joinFans), 0) * 100, 4) AS totalFirstChargeRate,
            ROUND(SUM(t.reward_users_total) / NULLIF(SUM(t.joinFans), 0) * 100, 4) AS avgRewardRate
        FROM (
                 SELECT
                     ai.anchor_id AS anchorId,
                     cja.joinFansNum,
                     ROUND(COALESCE(cja.total_amount, 0), 2) AS reward,
                     ar.total_reward totalReward,
                     ar.total_live_hours liveHours,
                     cja.fans_count AS joinFans,
                     ROUND(cja.joinFansNum / NULLIF(cja.fans_count, 0) * 100, 2) AS joinFansRate,
                     cja.first_charge_count AS firstCharge,
                     ROUND(cja.first_charge_count / NULLIF(cja.fans_count, 0) * 100, 2) AS firstChargeRate,
                     CASE
                         WHEN ar.is_live_ing = 1 THEN 1
                         WHEN DATE(alr.start_time) = CURDATE() THEN 3
                         ELSE 2
                         END AS liveStatus,
                     ROUND(COALESCE(cja.reward_users_total, 0) / NULLIF(cja.fans_count, 0) * 100, 2) AS rewardRate,
                     cja.reward_users_total
                 FROM crm_anchor_account_mapping aam
                          INNER JOIN crm_anchor_info ai ON aam.anchor_id = ai.anchor_id
                          INNER JOIN (
                     SELECT
                         aam.anchor_id,
                         SUM(a.total_reward_money) total_reward,
                         ROUND(SUM(a.total_live_hour), 2) total_live_hours,
                         MAX(a.is_live_ing) is_live_ing
                     FROM crm_anchor_account_mapping aam
                              INNER JOIN crm_anchor a ON aam.account_id = a.anchor_id
                     GROUP BY aam.anchor_id
                 ) ar ON ai.anchor_id = ar.anchor_id
                          LEFT JOIN (
                     SELECT
                         aam.anchor_id AS main_anchor_id,
                         SUM(cja.join_amt) AS total_amount,
                         COUNT(CASE WHEN cja.STATUS = 1 AND cja.join_amt > 0 THEN 1 END) AS joinFansNum,
                         COUNT(CASE WHEN cja.STATUS = 1 THEN 1 END) AS fans_count,
                         COUNT(CASE WHEN cja.first_charge_date IS NOT NULL THEN 1 END) AS first_charge_count,
                         COUNT(DISTINCT CASE WHEN cja.join_amt > 0 THEN cja.customer_id END) AS reward_users_total
                     FROM crm_anchor_account_mapping aam
                              LEFT JOIN (
                         SELECT
                             cja.*,
                             ao.operate_id AS current_operate_id
                         FROM
                             ( SELECT *, ROW_NUMBER() OVER ( PARTITION BY anchor_id, customer_id ORDER BY receive_time DESC ) rn FROM crm_customer_join_anchor WHERE STATUS = 1 ) cja
                                 INNER JOIN crm_anchor_operate ao ON cja.anchor_id = ao.anchor_id
                         WHERE
                             cja.rn = 1
                           AND cja.operate_id = ao.operate_id
                     ) cja ON aam.account_id = cja.anchor_id
                     GROUP BY
                         aam.anchor_id
                 ) cja ON cja.main_anchor_id = ai.anchor_id
                          LEFT JOIN (
                     SELECT anchor_id, MAX(start_time) AS start_time
                     FROM crm_anchor_live_record
                     GROUP BY anchor_id
                 ) alr ON alr.anchor_id IN (
                     SELECT account_id
                     FROM crm_anchor_account_mapping
                     WHERE anchor_id = ai.anchor_id
                 )
                          LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = aam.account_id
                          LEFT JOIN yooa_system.sys_user_pd up ON ao.operate_id = up.pd_user_id
                          LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
                          LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id
                 WHERE 1 = 1
                     ${query.params.dataScope}
                 GROUP BY ai.anchor_id
             ) t

    </select>

</mapper>
