package com.yooa.crm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.crm.api.domain.query.CustomerPitcherFriendQuery;
import com.yooa.crm.api.domain.vo.RegisterPitcherVo;
import com.yooa.crm.service.CustomerFriendService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;


@RestController
@RequestMapping("/customer/friend")
@RequiredArgsConstructor
@Validated
public class CustomerFriendController extends BaseController {

    private final CustomerFriendService customerFriendService;

    /**
     * 投放 - 注册列表
     */
    @GetMapping("/pitcher/list")
    public AjaxResult list(Page<RegisterPitcherVo> page, @Valid CustomerPitcherFriendQuery query) {
        return AjaxResult.success(page.setRecords(customerFriendService.pitcherRegisterList(page, query)));
    }

    /**
     * 投放 - 导出注册列表
     */
    @PostMapping("/pitcher/list/export")
    public void export(HttpServletResponse response, @Valid CustomerPitcherFriendQuery query) {
        List<RegisterPitcherVo> list = customerFriendService.pitcherRegisterList(null, query);
        // 处理大小号
        for (RegisterPitcherVo vo : list) {
            if ("0".equals(vo.getAccountType())) {
                vo.setAccountType("大号");
            } else {
                vo.setAccountType("小号");
            }
        }

        ExcelUtil<RegisterPitcherVo> util = new ExcelUtil<>(RegisterPitcherVo.class);
        util.exportExcel(response, list, "投放注册列表");
    }

}
