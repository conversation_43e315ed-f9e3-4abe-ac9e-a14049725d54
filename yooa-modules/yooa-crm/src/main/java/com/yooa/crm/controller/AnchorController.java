package com.yooa.crm.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.domain.SubscribeConvertDbData;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.mybatis.base.QueryEntity;
import com.yooa.common.security.utils.ExcelConvertUtil;
import com.yooa.crm.api.domain.CrmAnchor;
import com.yooa.crm.api.domain.CrmListenerKafkaErr;
import com.yooa.crm.api.domain.dto.AnchorInfoDto;
import com.yooa.crm.api.domain.query.AnchorDetailQuery;
import com.yooa.crm.api.domain.query.AnchorQuery;
import com.yooa.crm.api.domain.query.AnchorRewardQuery;
import com.yooa.crm.api.domain.query.AnchorRosterQuery;
import com.yooa.crm.api.domain.vo.AnchorDetailVo;
import com.yooa.crm.api.domain.vo.AnchorManageVo;
import com.yooa.crm.api.domain.vo.AnchorRewardVo;
import com.yooa.crm.api.domain.vo.AnchorVo;
import com.yooa.crm.producer.KafkaMessageProducer;
import com.yooa.crm.service.AnchorService;
import com.yooa.crm.service.ListenerKafkaErrService;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * 主播 - 控制层
 */
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/anchor-account")
public class AnchorController extends BaseController {

    private final AnchorService anchorService;

    private final ListenerKafkaErrService listenerKafkaErrService;

    private KafkaMessageProducer producer;

    /**
     * 根据主播id获取主播账号信息列表
     */
    @GetMapping("/list/{anchorId}")
    public AjaxResult listByAnchorId(@PathVariable Long anchorId) {
        return success(anchorService.listByAnchorId(anchorId));
    }

    /**
     * 工作台 (我的主播)
     * 运营查询旗下的所有主播数据
     *
     * @Date: 2025/03/07
     */
    @GetMapping("/getAnchorDetail")
    public AjaxResult getAnchorDetail(Page<AnchorDetailVo> page, @Validated AnchorDetailQuery anchorDetailQuery){
        return success(anchorService.getAnchorDetail(page,anchorDetailQuery));
    }


    /**
     * 导出 工作台 (我的主播)
     */
    @PostMapping("/getAnchorDetail/export")
    public void getAnchorDetailExport(HttpServletResponse response, Page<AnchorDetailVo> page, @Validated AnchorDetailQuery anchorDetailQuery) throws IllegalAccessException {
        page.setSize(-1);
        List<AnchorManageVo> anchorManageVoList = anchorService.getAnchorDetailExport(page, anchorDetailQuery);
        ExcelConvertUtil.process(anchorManageVoList);
        ExcelUtil<AnchorManageVo> util = new ExcelUtil<>(AnchorManageVo.class);
        util.exportExcel(response, anchorManageVoList, "工作台(我的主播)");
    }

    /**
     * 导入 工作台 (我的主播)
     */
    @PostMapping("/anchorInfo/import")
    public AjaxResult anchorInfoImport(MultipartFile file) throws Exception {
        return success(anchorService.anchorInfoImport(file));
    }

    /**
     * 下载模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", StrUtil.format("attachment;filename=anchor_template_{}.xls", DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN)));

        ExportParams exportParams = new ExportParams("", "主播模板");

        // 空数据列表，仅生成表头
        List<AnchorInfoDto> emptyList = new ArrayList<>();

        // 生成Excel
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, AnchorInfoDto.class, emptyList);

        Sheet sheet = workbook.getSheetAt(0);
        if(sheet.getRow(0) != null){
            sheet.removeRow(sheet.getRow(0));
            sheet.shiftRows(1,sheet.getLastRowNum(),-1);
        }
        workbook.write(response.getOutputStream());
        workbook.close();

    }



    /**
     * 主播打赏记录
     */
    @GetMapping("/getAnchorReward")
    public AjaxResult getAnchorReward(Page page, @Validated AnchorRewardQuery anchorRewardQuery) throws ExecutionException, InterruptedException {
        return success(anchorService.getAnchorReward(page, anchorRewardQuery));
    }


    /**
     * 主播列表
     * @return
     */
    @GetMapping("/getAnchorList")
    public AjaxResult getAnchorList(Page page, AnchorQuery anchorQuery) throws ExecutionException, InterruptedException {
//        return success(page.setRecords(anchorService.getAnchorList(page,anchorQuery)));
        List<AnchorVo>  anchorList = anchorService.getAnchorListExport(page, anchorQuery);
        return success(page.setRecords(anchorList));
    }

    /**
     * 主播列表数据统计
     */
    @GetMapping("/getAnchorListCollect")
    public AjaxResult getAnchorListCollect(AnchorQuery anchorQuery){
        return success(anchorService.getAnchorListCollect(anchorQuery));
    }

    /**
     * 主播列表 (导出)
     * @return
     */
    @PostMapping("/getAnchorListExport")
    public void getAnchorListExport(HttpServletResponse response,Page page,AnchorQuery anchorQuery) throws ExecutionException, InterruptedException {
        page.setSize(-1);
        List<AnchorVo>  anchorList = anchorService.getAnchorListExport(page, anchorQuery);
        ExcelUtil<AnchorVo> util = new ExcelUtil<>(AnchorVo.class);
        util.exportExcel(response, anchorList, "主播列表");
    }

    /**
     * 主播列表打赏数据和直播时长数据查询
     */
    @GetMapping("/getAnchorCollect")
    public AjaxResult getAnchorCollect(QueryEntity query){
       return success(anchorService.getAnchorCollect(query));
    }

    @GetMapping("/test")
    public void test(){
        List<CrmListenerKafkaErr> busCmfAgentAdminAndUser = listenerKafkaErrService.getBaseMapper().selectList(new LambdaQueryWrapper<CrmListenerKafkaErr>()
                .eq(CrmListenerKafkaErr::getTopicName, "_cdc_cmf_users"));
        busCmfAgentAdminAndUser.stream().sorted(Comparator.comparing(CrmListenerKafkaErr::getCreateTime));

        busCmfAgentAdminAndUser.forEach(
                message -> {
                    SubscribeConvertDbData data = new SubscribeConvertDbData();
                    data.setFieldList(JSON.parseArray(message.getMsg(), SubscribeConvertDbData.Field.class));
                    data.setDbTableName("test_pdlive");
                    data.setOperatorType(message.getOperatorType());
                    data.setDbName("_cdc_cmf_users");
                    producer.sendMessage("_cdc_cmf_users",JSONObject.toJSONString(data));
                }
        );

    }
}
