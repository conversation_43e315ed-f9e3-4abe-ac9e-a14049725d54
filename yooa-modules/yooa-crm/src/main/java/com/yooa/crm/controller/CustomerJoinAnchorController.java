package com.yooa.crm.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.crm.api.domain.query.CustomerJoinAnchorPitcherQuery;
import com.yooa.crm.api.domain.query.CustomerJoinAnchorQuery;
import com.yooa.crm.api.domain.query.CustomerOperateJoinAnchorQuery;
import com.yooa.crm.api.domain.vo.CustomerJoinAnchorPitcherVo;
import com.yooa.crm.api.domain.vo.CustomerJoinAnchorVo;
import com.yooa.crm.api.domain.vo.CustomerOperateJoinAnchorVo;
import com.yooa.crm.mapper.CrmCustomerJoinAnchorMapper;
import com.yooa.crm.service.CustomerJoinAnchorService;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/customer/anchor")
public class CustomerJoinAnchorController extends BaseController {

    private final CustomerJoinAnchorService customerJoinAnchorService;

    private final CrmCustomerJoinAnchorMapper customerJoinAnchorMapper;

    /**
     * 一交列表
     */
    @GetMapping("/list")
    public AjaxResult list(Page page, CustomerJoinAnchorQuery customerJoinAnchorQuery){
        return success(page.setRecords(customerJoinAnchorService.getList(page, customerJoinAnchorQuery)));
    }

    /**
     * 运营模块一交列表
     */
    @GetMapping("/getOperatorJoinAnchorList")
    public AjaxResult getOperatorJoinAnchorList(Page page, CustomerOperateJoinAnchorQuery query){
        return success(page.setRecords(customerJoinAnchorService.getOperatorJoinAnchorList(page,query)));
    }

    /**
     * 运营模块一交列表(导出)
     */
    @PostMapping("/getOperatorJoinAnchorList/export")
    public void getOperatorJoinAnchorListExport(HttpServletResponse response, CustomerOperateJoinAnchorQuery query){
        List<CustomerOperateJoinAnchorVo> operatorJoinAnchorList = customerJoinAnchorService.getOperatorJoinAnchorListExport(query);
        ExcelUtil<CustomerOperateJoinAnchorVo> util = new ExcelUtil<>(CustomerOperateJoinAnchorVo.class);
        util.exportExcel(response, operatorJoinAnchorList, "运营一交列表导出");
    }

    /**
     * 一交列表 (导出)
     */
    @PostMapping("/listExport")
    public void listExport(HttpServletResponse response,Page page, CustomerJoinAnchorQuery customerJoinAnchorQuery) throws Exception {
        page.setSize(-1);
        List<CustomerJoinAnchorVo>  anchorList = customerJoinAnchorService.getListExport(page, customerJoinAnchorQuery);
        List<CustomerJoinAnchorVo> result = anchorList.stream().map(customerJoinAnchorVo -> {
            Integer isItValid = customerJoinAnchorVo.getIsItValid();
            if (isItValid == 1) {
                // 无效类型区分
//                LocalDateTime beginTime = customerJoinAnchorVo.getBeginTime();
//                LocalDateTime endTime = customerJoinAnchorVo.getEndTime();

                if(StrUtil.isNotBlank(customerJoinAnchorVo.getBindType()) && StrUtil.equals(customerJoinAnchorVo.getBindType(),"0")) {
                    customerJoinAnchorVo.setInvalidType(3);
                }else {
                    // 取最早交接的id 只有首次交接的算有效
                    long firstHandoverId = customerJoinAnchorMapper.getFirstHandoverId(customerJoinAnchorVo.getCustomerId());
                    customerJoinAnchorVo.setInvalidType(firstHandoverId == customerJoinAnchorVo.getId() ? 2 : 1);
                }
            }
            return customerJoinAnchorVo;
        }).toList();
        ExcelUtil<CustomerJoinAnchorVo> util = new ExcelUtil<>(CustomerJoinAnchorVo.class);
        util.exportExcel(response, result, "一交列表");
    }

    /**
     * 投手 - 一交列表
     */
    @GetMapping("/pitcher/list")
    public AjaxResult pitcherList(Page<CustomerJoinAnchorPitcherVo> page, CustomerJoinAnchorPitcherQuery query) {
        return success(page.setRecords(customerJoinAnchorService.getPitcherList(page, query)));
    }

    /**
     * 投手 - 一交列表导出
     */
    @PostMapping("/pitcher/listExport")
    public void pitcherListExport(HttpServletResponse response,Page page, CustomerJoinAnchorPitcherQuery customerJoinAnchorQuery) {
        page.setSize(-1);
        List<CustomerJoinAnchorPitcherVo>  anchorList = customerJoinAnchorService.getPitcherListExport(page, customerJoinAnchorQuery);
        List<CustomerJoinAnchorPitcherVo> result = anchorList.stream().map(customerJoinAnchorVo -> {
            Integer isItValid = customerJoinAnchorVo.getIsItValid();
            if (isItValid == 1) {
                // 无效类型区分
                LocalDateTime beginTime = customerJoinAnchorVo.getBeginTime();
                LocalDateTime endTime = customerJoinAnchorVo.getEndTime();

                if(StrUtil.isNotBlank(customerJoinAnchorVo.getBindType()) && StrUtil.equals(customerJoinAnchorVo.getBindType(),"0")) {
                    customerJoinAnchorVo.setInvalidType(3);
                }else if (beginTime != null && endTime != null) {
                    // 好友绑定表中的结束时间大于开始时间 属于非最早交接的无效数据
                    customerJoinAnchorVo.setInvalidType(endTime.isAfter(beginTime) ? 1 : 2);
                }
            }
            return customerJoinAnchorVo;
        }).toList();
        ExcelUtil<CustomerJoinAnchorPitcherVo> util = new ExcelUtil<>(CustomerJoinAnchorPitcherVo.class);
        util.exportExcel(response, result, "一交列表");
    }

    public static void setExportExcelFormat(HttpServletResponse response, Workbook workbook, String fileName) throws Exception {
        response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), "ISO8859-1"));
        response.setHeader("Pargam", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        ServletOutputStream outStream = null;
        try {
            outStream = response.getOutputStream();
            workbook.write(outStream);
        } catch (Exception e) {
            e.printStackTrace();

        } finally {
            outStream.flush();
            outStream.close();
        }
    }

}
