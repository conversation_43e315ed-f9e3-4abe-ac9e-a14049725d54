package com.yooa.crm.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.log.annotation.Log;
import com.yooa.common.log.enums.BusinessType;
import com.yooa.common.security.annotation.RequiresPermissions;
import com.yooa.crm.api.domain.CrmFriendArea;
import com.yooa.crm.service.FriendAreaService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 好友地区 - 控制层
 */
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/friendArea")
public class FriendAreaController extends BaseController {

    private final FriendAreaService friendAreaService;

    /**
     * 获取好友地区列表
     */
    @RequiresPermissions("crm:friendArea:list")
    @GetMapping("/list")
    public AjaxResult list(CrmFriendArea friendArea) {
        List<CrmFriendArea> list = friendAreaService.selectFriendAreaList(friendArea);
        return success(list);
    }

    /**
     * 获取好友地区树形结构列表
     */
    @GetMapping("/tree")
    public AjaxResult tree() {
        List<CrmFriendArea> parentList = friendAreaService.list(
                Wrappers.<CrmFriendArea>lambdaQuery()
                        .eq(CrmFriendArea::getParentId, 0L)
                        .orderByAsc(CrmFriendArea::getSort)
        );
        parentList.forEach(parent -> {
            parent.setChildren(friendAreaService.list(
                    Wrappers.<CrmFriendArea>lambdaQuery()
                            .eq(CrmFriendArea::getParentId, parent.getId())
                            .orderByAsc(CrmFriendArea::getSort))
            );
        });
        return success(parentList);
    }

    /**
     * 查询好友地区列表（排除节点）
     */
    @RequiresPermissions("crm:friendArea:list")
    @GetMapping("/list/exclude/{areaId}")
    public AjaxResult excludeChild(@PathVariable(value = "areaId", required = false) Long areaId) {
        List<CrmFriendArea> areas = friendAreaService.selectFriendAreaList(new CrmFriendArea());
        areas.removeIf(d -> d.getId().intValue() == areaId || isChildArea(areas, areaId, d.getId()));
        return success(areas);
    }

    /**
     * 根据好友地区编号获取详细信息
     */
    @RequiresPermissions("crm:friendArea:query")
    @GetMapping(value = "/{areaId}")
    public AjaxResult getInfo(@PathVariable Long areaId) {
        return success(friendAreaService.selectFriendAreaById(areaId));
    }

    /**
     * 新增好友地区
     */
    @RequiresPermissions("crm:friendArea:add")
    @Log(title = "好友地区管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody CrmFriendArea friendArea) {
        if (!friendAreaService.checkAreaNameUnique(friendArea)) {
            return error("新增地区'" + friendArea.getArea() + "'失败，地区名称已存在");
        }
        return toAjax(friendAreaService.insertFriendArea(friendArea));
    }

    /**
     * 修改好友地区
     */
    @RequiresPermissions("crm:friendArea:edit")
    @Log(title = "好友地区管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody CrmFriendArea friendArea) {
        Long areaId = friendArea.getId();
        if (!friendAreaService.checkAreaNameUnique(friendArea)) {
            return error("修改地区'" + friendArea.getArea() + "'失败，地区名称已存在");
        } else if (friendArea.getParentId().equals(areaId)) {
            return error("修改地区'" + friendArea.getArea() + "'失败，上级地区不能是自己");
        }
        return toAjax(friendAreaService.updateFriendArea(friendArea));
    }

    /**
     * 删除好友地区
     */
    @RequiresPermissions("crm:friendArea:remove")
    @Log(title = "好友地区管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{areaId}")
    public AjaxResult remove(@PathVariable Long areaId) {
        if (friendAreaService.hasChildByAreaId(areaId)) {
            return warn("存在下级地区,不允许删除");
        }
        return toAjax(friendAreaService.deleteFriendAreaById(areaId));
    }

    /**
     * 判断是否为子地区
     */
    private boolean isChildArea(List<CrmFriendArea> areas, Long parentId, Long childId) {
        for (CrmFriendArea area : areas) {
            if (area.getId().equals(childId) && area.getParentId().equals(parentId)) {
                return true;
            }
            if (area.getParentId().equals(childId)) {
                return isChildArea(areas, parentId, area.getId());
            }
        }
        return false;
    }
}
