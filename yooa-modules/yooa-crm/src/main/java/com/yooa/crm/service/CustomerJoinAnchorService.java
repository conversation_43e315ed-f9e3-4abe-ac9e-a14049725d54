package com.yooa.crm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.crm.api.domain.CrmCustomerJoinAnchor;
import com.yooa.crm.api.domain.query.CustomerJoinAnchorPitcherQuery;
import com.yooa.crm.api.domain.query.CustomerJoinAnchorQuery;
import com.yooa.crm.api.domain.query.CustomerOperateJoinAnchorQuery;
import com.yooa.crm.api.domain.vo.CustomerJoinAnchorPitcherVo;
import com.yooa.crm.api.domain.vo.CustomerJoinAnchorVo;
import com.yooa.crm.api.domain.vo.CustomerOperateJoinAnchorVo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户主播交接 - 服务层
 */
public interface CustomerJoinAnchorService extends IService<CrmCustomerJoinAnchor> {

    List<CrmCustomerJoinAnchor> getCustomerJoinAnchorByCustomerId(Long customerId, LocalDateTime addTime);

    List<CustomerJoinAnchorVo> getList(Page page, CustomerJoinAnchorQuery customerJoinAnchorQuery);

    List<CustomerOperateJoinAnchorVo> getOperatorJoinAnchorList(Page page, CustomerOperateJoinAnchorQuery query);

    List<CustomerJoinAnchorVo> getListExport(Page page, CustomerJoinAnchorQuery customerJoinAnchorQuery);

    List<CustomerOperateJoinAnchorVo> getOperatorJoinAnchorListExport(CustomerOperateJoinAnchorQuery query);

    /**
     * 投手一交列表
     */
    List<CustomerJoinAnchorPitcherVo> getPitcherList(Page<CustomerJoinAnchorPitcherVo> page, CustomerJoinAnchorPitcherQuery query);

    /**
     * 投手一交列表导出
     */
    List<CustomerJoinAnchorPitcherVo> getPitcherListExport(Page page, CustomerJoinAnchorPitcherQuery customerJoinAnchorQuery);

    Long existsDuplicateBinding(Long customerId,Long friendId, Long adminid);
}
