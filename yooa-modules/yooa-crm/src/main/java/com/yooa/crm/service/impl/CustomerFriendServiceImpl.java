package com.yooa.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.common.datascope.annotation.DataScope;
import com.yooa.crm.api.domain.CrmCustomerFriend;
import com.yooa.crm.api.domain.query.CustomerFriendQuery;
import com.yooa.crm.api.domain.query.CustomerPitcherFriendQuery;
import com.yooa.crm.api.domain.vo.RegisterPitcherVo;
import com.yooa.crm.api.domain.vo.RegisterVo;
import com.yooa.crm.mapper.CrmCustomerFriendMapper;
import com.yooa.crm.mapper.CrmFriendMapper;
import com.yooa.crm.service.CustomerFriendService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户好友关联 - 服务实现层
 */
@Service
@AllArgsConstructor
public class CustomerFriendServiceImpl extends ServiceImpl<CrmCustomerFriendMapper, CrmCustomerFriend> implements CustomerFriendService {

    private final CrmFriendMapper friendMapper;
    @Override
    public CrmCustomerFriend getBindByCustomerId(Long customerId) {
        return getOne(new LambdaQueryWrapper<CrmCustomerFriend>()
                .eq(CrmCustomerFriend::getCustomerId, customerId)
                .eq(CrmCustomerFriend::getStatus, 0)
                .last("limit 1")
        );
    }
    
    @Override
    public List<CrmCustomerFriend> getBindByFriendId(Long friendId) {
        return list(new LambdaQueryWrapper<CrmCustomerFriend>()
                .eq(CrmCustomerFriend::getFriendId, friendId)
                .eq(CrmCustomerFriend::getStatus, 0)
        );
    }

    @Override
    @DataScope(deptAlias = "au", userAlias = "au")
    public List<RegisterPitcherVo> pitcherRegisterList(Page<RegisterPitcherVo> page, CustomerPitcherFriendQuery query) {
        return friendMapper.selectPitcherRegisterList(page, query);
    }
}
