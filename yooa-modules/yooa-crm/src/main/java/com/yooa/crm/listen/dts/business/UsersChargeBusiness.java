package com.yooa.crm.listen.dts.business;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableList;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yooa.common.core.constant.KafkaBusinessTopicConstants;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.domain.SubscribeConvertDbData;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.core.utils.LocalDateUtil;
import com.yooa.crm.api.domain.*;
import com.yooa.crm.mapper.*;
import com.yooa.crm.service.CustomerOrderService;
import com.yooa.crm.service.FriendService;
import com.yooa.extend.api.RemoteVermicelliService;
import com.yooa.extend.api.domain.ExtendVermicelli;
import com.yooa.extend.api.domain.OperateVermicelli;
import com.yooa.extend.api.domain.VipVermicelli;
import com.yooa.system.api.RemoteUserService;
import com.yooa.system.api.domain.query.UserQuery;
import com.yooa.system.api.domain.vo.SysUserVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.util.*;
import java.util.stream.Collectors;

import static com.yooa.common.core.utils.ObjectConvertUtil.convertToLong;

/**
 *
 */
@Service
@RequiredArgsConstructor
public class UsersChargeBusiness extends AbstractBaseBusiness {

    private final CustomerOrderService customerOrderService;
    private final CrmCustomerOrderMapper orderMapper;
    private final CrmCustomerMapper crmCustomerMapper;
    private final CrmCustomerFriendMapper crmCustomerFriendMapper;
    private final FriendService friendService;
    private final RemoteUserService remoteUserService;
    private final RemoteVermicelliService remoteVermicelliService;
    private final CrmCustomerJoinAnchorMapper customerJoinAnchorMapper;
    private final CrmCustomerJoinServeMapper customerJoinServeMapper;

    @Override
    public String getTopic() {
        return KafkaBusinessTopicConstants.BUS_CMF_USERS_CHARGE;
    }


    @Override
    protected List<String> getFilterList() {
        return ImmutableList.of("status", "id", "orderno", "coin", "coin_give2", "yuanbao", "yuanbao_give", "addtime", "uid", "admin_id", "serve_id");
    }

    @Override
    protected void handleInsert(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        handleBusinessLogic(fieldMap, 1);
    }

    @Override
    protected void handleUpdate(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        handleBusinessLogic(fieldMap, 2);
    }

    @Override
    protected void handleDelete(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {

    }

    protected void handleBusinessLogic(Map<String, SubscribeConvertDbData.Field> fieldMap, Integer operatorType) {
        // 原状态 "0-未支付","1-已完成","2-用户退款"
        String beforeOrderStatus = "0";
        // 原推广id
        String beforeExtendId = "0";
        // 原客服id
        String beforeServeId = "0";
        if (operatorType == 2) { // 新增的时候没有原数据
            beforeOrderStatus = getBeforeValueByFieldName("status", fieldMap);
            beforeExtendId = getBeforeValueByFieldName("admin_id", fieldMap);
            beforeServeId = getBeforeValueByFieldName("serve_id", fieldMap);
        }

        // 现状态
        String afterOrderStatus = getAfterValueByFieldName("status", fieldMap);
        // 钻石数
        String coin = getAfterValueByFieldName("coin", fieldMap);
        // 赠送金币数
        String coinGive = getAfterValueByFieldName("coin_give2", fieldMap);
        // 元宝数
        String yuanbao = getAfterValueByFieldName("yuanbao", fieldMap);
        // 赠送元宝数
        String yuanbaoGive = getAfterValueByFieldName("yuanbao_give", fieldMap);
        // 订单发起时间
        String addTime = getAfterValueByFieldName("addtime", fieldMap);
        // 客户id
        String customerId = getAfterValueByFieldName("uid", fieldMap);
        // 现推广id
        String afterExtendId = getAfterValueByFieldName("admin_id", fieldMap);
        // 现客服id
        String afterServeId = getAfterValueByFieldName("serve_id", fieldMap);

        // 组装订单
        CrmCustomerOrder order = getCrmCustomerOrder(coin, coinGive, yuanbao, yuanbaoGive, addTime, afterExtendId, afterServeId, customerId);

        /**
         * 业务处理的四种情况
         * 情况1: 已完成
         * 情况2: 已退款
         * 情况3: 推广归属变动
         * 情况4: 客服归属变动
         */
        if (StrUtil.isNotEmpty(afterOrderStatus) && afterOrderStatus.equals("1")) {

            // 情况1(推广)
            if (!StrUtil.equals(beforeExtendId, afterExtendId)) {   // 推广归属变动
                judgmentExtendVermicelliAdd(order.getCustomerId(), order.getOrderTime().toLocalDate());
                if (!beforeExtendId.equals("0")) {                  // 原归属不等于0
                    // 情况3
                    judgmentExtendVermicelliDelete(convertToLong(beforeExtendId), order.getCustomerId(), order.getOrderTime().toLocalDate());
                }
            } else if (!afterExtendId.equals("0")) {                // 订单状态变动
                judgmentExtendVermicelliAdd(order.getCustomerId(), order.getOrderTime().toLocalDate());
            }
            // 情况1(客服)
            if (!StrUtil.equals(beforeServeId, afterServeId)) {
                judgmentVipVermicelliAdd(order.getCustomerId(), convertToLong(afterServeId), order.getOrderTime().toLocalDate());
                if (!beforeServeId.equals("0")) {                  // 原归属不等于0
                    // 情况4
                    judgmentVipVermicelliDelete(convertToLong(beforeServeId), order.getCustomerId(), order.getOrderTime().toLocalDate());
                }
            } else if (!afterServeId.equals("0")) { // 订单状态变动
                judgmentVipVermicelliAdd(order.getCustomerId(), convertToLong(afterServeId), order.getOrderTime().toLocalDate());
            }


            // 订单完成时去执行运营首充判断
            operateFirstCharge(order.getCustomerId(), order.getOrderTime());

        } else if (StrUtil.isNotEmpty(afterOrderStatus) && afterOrderStatus.equals("2")) {
            // 情况2(推广)
            if (ObjUtil.isNotNull(afterExtendId) && !afterExtendId.equals("0")) {
                judgmentExtendVermicelliDelete(convertToLong(afterExtendId), order.getCustomerId(), order.getOrderTime().toLocalDate());
            }

            // 情况2(客服)
            if (ObjUtil.isNotNull(afterServeId) && !afterServeId.equals("0")) {
                judgmentVipVermicelliDelete(convertToLong(beforeServeId), order.getCustomerId(), order.getOrderTime().toLocalDate());
            }
        }
    }

    /**
     * 订单金额
     * 订单金额计算公式 ROUND((IFNULL(coin,0) + IFNULL(coin_give2,0)) / 100,2) + (IFNULL(yuanbao,0) + IFNULL(yuanbao_give,0)) AS order_money
     * ((钻石数 + 赠送钻石数) / 100) + (元宝数 + 赠送元宝数)
     */
    private BigDecimal getOrderMoney(String coin, String coinGive, String yuanbao, String yuanbaoGive) {
        // 钻石数
        coin = StrUtil.isNotBlank(coin) ? coin : "0";
        // 赠送钻石数
        coinGive = StrUtil.isNotBlank(coinGive) ? coinGive : "0";
        // 元宝数
        yuanbao = StrUtil.isNotBlank(yuanbao) ? yuanbao : "0";
        // 赠送元宝数
        yuanbaoGive = StrUtil.isNotBlank(yuanbaoGive) ? yuanbaoGive : "0";
        return NumberUtil.add(coin, coinGive)
                .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP)
                .add(NumberUtil.add(yuanbao, yuanbaoGive));
    }

    /**
     * 组装crmCustomerOrder数据
     */
    private CrmCustomerOrder getCrmCustomerOrder(String coin, String coinGive, String yuanbao, String yuanbaoGive,
                                                 String addTime, String extendId, String serveId, String customerId) {
        // 获取订单金额
        BigDecimal orderMoney = getOrderMoney(coin, coinGive, yuanbao, yuanbaoGive);

        CrmCustomerOrder crmCustomerOrder = new CrmCustomerOrder();
        // 订单金额
        crmCustomerOrder.setOrderMoney(orderMoney);
        // 订单发起时间
        crmCustomerOrder.setOrderTime(LocalDateUtil.epochSecondToLocalDateTime(addTime));
        // 推广id
        crmCustomerOrder.setPyExtendId(convertToLong(extendId));
        // 客服id
        crmCustomerOrder.setPyServeId(convertToLong(serveId));
        // 客户id
        crmCustomerOrder.setCustomerId(convertToLong(customerId));
        return crmCustomerOrder;
    }

    /**
     * 判断推广粉丝登记(新增)
     *
     * @param orderTime  订单发起时间
     * @param customerId 订单客户id
     */
    private void judgmentExtendVermicelliAdd(Long customerId, LocalDate orderTime) {
        // 查出此客户的关联好友
        CrmCustomerFriend friendCustomer = crmCustomerFriendMapper.selectOne(new LambdaQueryWrapper<CrmCustomerFriend>()
                .eq(CrmCustomerFriend::getCustomerId, customerId)
                .eq(CrmCustomerFriend::getStatus, 0)
                .apply("end_time IS NULL")
                .last("limit 1"));

        if (ObjUtil.isNotNull(friendCustomer)) {        // 客户没有绑定好友不进行粉丝登记
            // 查询好友下绑定的客户
            List<CrmCustomerFriend> customerFriends = crmCustomerFriendMapper.selectList(new LambdaQueryWrapper<CrmCustomerFriend>()
                    .in(CrmCustomerFriend::getFriendId, friendCustomer.getFriendId())
                    .eq(CrmCustomerFriend::getStatus, 0)
                    .apply("end_time IS NULL"));
            // 好友
            CrmFriend friend = friendService.getById(friendCustomer.getFriendId());
            // 客户
            CrmCustomer customer = crmCustomerMapper.selectById(customerId);
            // 好友领取人
            List<SysUserVo> userVoList = remoteUserService.getUserList(
                    UserQuery.builder().pdUserId(CollUtil.newArrayList(customer.getExtendId())).build(),
                    SecurityConstants.INNER).getData();
            SysUserVo userVo = userVoList.get(0);

            if (ObjUtil.isNotNull(userVo)) {
                // 获取传入时间的这个月的订单(并且是属于这个好友下的客户集的订单)已完成订单
                List<CrmCustomerOrder> orderList = customerOrderService.list(new LambdaQueryWrapper<CrmCustomerOrder>()
                        .in(CrmCustomerOrder::getCustomerId, customerFriends.stream().map(CrmCustomerFriend::getCustomerId).distinct().toList())
                        .in(CrmCustomerOrder::getPyExtendId, userVo.getPdUserId())    // 好友领取人绑定的PYID集
                        .eq(CrmCustomerOrder::getOrderStatus, 1)     // 已完成
                        .ge(CrmCustomerOrder::getOrderTime, orderTime.withDayOfMonth(1))// 月初时间
                        .orderByAsc(CrmCustomerOrder::getOrderTime));    // 订单排序

                if (CollUtil.isNotEmpty(orderList)) {
                    // 推广粉丝登记模版
                    ExtendVermicelli vermicelli = new ExtendVermicelli();
                    vermicelli.setFriendId(friend.getFriendId());
                    vermicelli.setCustomerIds(customerFriends.stream().map(CrmCustomerFriend::getCustomerId).distinct().toList()
                            .stream().map(String::valueOf).collect(Collectors.joining(",")));
                    vermicelli.setExtendId(userVo.getUserId());
                    vermicelli.setExtendDeptId(userVo.getDeptId());
                    vermicelli.setCreateTime(LocalDateTime.now());
                    vermicelli.setRemark("充值订单产生粉丝登记");
                    vermicelli.setCreateBy(1L);

                    // 好友已录入的粉丝登记
                    List<ExtendVermicelli> extendVermicelliList = remoteVermicelliService.selExtendVermicelliList(
                            CollUtil.newArrayList(userVo.getUserId()),
                            null,
                            CollUtil.newArrayList(friend.getFriendId()),
                            null,
                            null,
                            SecurityConstants.INNER).getData();

                    boolean blsc = extendVermicelliList.stream().count() == 0;
                    boolean bl200 = extendVermicelliList.stream().filter(e -> e.getFansType() == 0).count() == 0;
                    boolean bl500 = extendVermicelliList.stream().filter(e -> e.getFansType() == 1).count() == 0;

                    if (blsc) {
                        vermicelli.setFansType(5);
                        vermicelli.setRecordDate(orderList.stream().map(CrmCustomerOrder::getOrderTime).min(LocalDateTime::compareTo).get().toLocalDate());
                        remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                    }

                    if (bl200 || bl500) {
                        // 按天分组并累加 orderMoney
                        Map<LocalDate, BigDecimal> dailyOrderMoneyMap = orderList.stream()
                                .collect(Collectors.groupingBy(
                                        order -> order.getOrderTime().toLocalDate(), // 按天分组
                                        Collectors.reducing(BigDecimal.ZERO, order -> order.getOrderMoney(), BigDecimal::add) // 累加 orderMoney
                                ));
                        if (bl200) {
                            LocalDate firstGreaterThan200 = dailyOrderMoneyMap.entrySet().stream()
                                    .sorted(Map.Entry.comparingByKey())         // 排序
                                    .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(200)) >= 0)
                                    .map(Map.Entry::getKey)
                                    .findFirst()
                                    .orElse(null);

                            if (ObjUtil.isNotNull(firstGreaterThan200)) {
                                vermicelli.setFansType(0);
                                vermicelli.setRecordDate(firstGreaterThan200);
                                remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                            }
                        }
                        if (bl500) {
                            LocalDate firstGreaterThan500 = dailyOrderMoneyMap.entrySet().stream()
                                    .sorted(Map.Entry.comparingByKey())         // 排序
                                    .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(500)) >= 0)
                                    .map(Map.Entry::getKey)
                                    .findFirst()
                                    .orElse(null);

                            if (ObjUtil.isNotNull(firstGreaterThan500)) {
                                vermicelli.setFansType(1);
                                vermicelli.setRecordDate(firstGreaterThan500);
                                remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                            }
                        }
                    }

                    boolean bl5k = extendVermicelliList.stream().filter(e -> e.getFansType() == 2
                            && e.getRecordDate().getMonthValue() == orderTime.getMonthValue()
                            && e.getRecordDate().getYear() == orderTime.getYear()).count() == 0;
                    boolean bl5w = extendVermicelliList.stream().filter(e -> e.getFansType() == 3
                            && e.getRecordDate().getMonthValue() == orderTime.getMonthValue()
                            && e.getRecordDate().getYear() == orderTime.getYear()).count() == 0;

                    if (bl5k || bl5w) {
                        boolean bl5kOlb = extendVermicelliList.stream().filter(v -> v.getFansType() == 2).count() == 0;
                        boolean bl5wOlb = extendVermicelliList.stream().filter(v -> v.getFansType() == 3).count() == 0;

                        BigDecimal monthUp = orderList.stream().map(CrmCustomerOrder::getOrderMoney).reduce(BigDecimal.ZERO, BigDecimal::add);      // 一个月的充值数

                        if (bl5k && monthUp.compareTo(BigDecimal.valueOf(5000)) >= 0) {
                            vermicelli.setFansType(2);
                            vermicelli.setStatus(bl5kOlb ? "0" : "1");
                            vermicelli.setRecordDate(orderList.stream()
                                    .reduce(
                                            new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                            (acc, order) -> {
                                                BigDecimal newAccumulated = acc.getKey().add(order.getOrderMoney());
                                                if (newAccumulated.compareTo(BigDecimal.valueOf(5000)) >= 0) {    // 统计第一次累加到5千时的订单时间
                                                    return new AbstractMap.SimpleEntry<>(newAccumulated, order.getOrderTime());
                                                } else {
                                                    return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                }
                                            },
                                            (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                    ).getValue().toLocalDate());
                            remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                        }
                        if (bl5w && monthUp.compareTo(BigDecimal.valueOf(50000)) >= 0) {
                            vermicelli.setFansType(3);
                            vermicelli.setStatus(bl5wOlb ? "0" : "1");
                            vermicelli.setRecordDate(orderList.stream()
                                    .reduce(
                                            new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                            (acc, order) -> {
                                                BigDecimal newAccumulated = acc.getKey().add(order.getOrderMoney());
                                                if (newAccumulated.compareTo(BigDecimal.valueOf(50000)) >= 0) {   // 统计第一次累加到5万时的订单时间
                                                    return new AbstractMap.SimpleEntry<>(newAccumulated, order.getOrderTime());
                                                } else {
                                                    return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                }
                                            },
                                            (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                    ).getValue().toLocalDate());
                            remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                        }
                    }

                    // 总充值
                    BigDecimal money = orderMapper.calculationExtendTotalMoney(customerFriends.stream().map(CrmCustomerFriend::getCustomerId).distinct().toList(), userVo.getPdUserId());

                    boolean bl10w = extendVermicelliList.stream().filter(e -> e.getFansType() == 6).count() == 0;
                    boolean bl40w = extendVermicelliList.stream().filter(e -> e.getFansType() == 7).count() == 0;
                    boolean bl100w = extendVermicelliList.stream().filter(e -> e.getFansType() == 8).count() == 0;
                    if (bl10w && money.compareTo(BigDecimal.valueOf(100000)) >= 0) {
                        vermicelli.setFansType(6);
                        vermicelli.setRecordDate(LocalDate.now());
                        remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                    } else if (bl40w && money.compareTo(BigDecimal.valueOf(400000)) >= 0) {
                        vermicelli.setFansType(7);
                        vermicelli.setRecordDate(LocalDate.now());
                        remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                    } else if (bl100w && money.compareTo(BigDecimal.valueOf(1000000)) >= 0) {
                        vermicelli.setFansType(8);
                        vermicelli.setRecordDate(LocalDate.now());
                        remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                    }
                }
            }
        }
    }

    /**
     * 判断推广粉丝登记(删除/修改)
     *
     * @param orderTime  订单发起时间
     * @param customerId 订单客户id
     * @param pdExtendId pd推广id
     */
    protected void judgmentExtendVermicelliDelete(Long pdExtendId, Long customerId, LocalDate orderTime) {
        List<SysUserVo> userVoList = remoteUserService.getUserList(UserQuery.builder()
                .pdUserId(CollUtil.newArrayList(pdExtendId))
                .build(), SecurityConstants.INNER).getData();
        if (CollUtil.isNotEmpty(userVoList)) {
            if (userVoList.size() != 1) {
                throw new ServiceException("充值订单业务处理异常:[" + pdExtendId + "]存在多个OA用户绑定!");
            }
            SysUserVo userVo = userVoList.get(0);
            List<ExtendVermicelli> vermicelliList = remoteVermicelliService.selOrderExtendVermicelliList(userVo.getUserId(), customerId, orderTime.toString(), SecurityConstants.INNER).getData();

            if (CollUtil.isNotEmpty(vermicelliList)) {
                List<Long> customerIds = vermicelliList.stream()
                        .flatMap(v ->
                                Arrays.stream(v.getCustomerIds().split(","))
                        )
                        .map(Long::parseLong)
                        .distinct()
                        .collect(Collectors.toList());

                // 获取传入时间的这个月的订单(并且是属于这个好友下的客户集的订单)已完成订单
                List<CrmCustomerOrder> orderList = customerOrderService.list(new LambdaQueryWrapper<CrmCustomerOrder>()
                        .in(CrmCustomerOrder::getCustomerId, customerIds)
                        .in(CrmCustomerOrder::getPyExtendId, userVo.getPdUserId())    // 以前达成的粉丝登记的推广归属
                        .eq(CrmCustomerOrder::getOrderStatus, 1)     // 已完成
                        .ge(CrmCustomerOrder::getOrderTime, orderTime.withDayOfMonth(1))// 月初时间
                        .orderByAsc(CrmCustomerOrder::getOrderTime));    // 订单排序

                if (CollUtil.isEmpty(orderList)) {  // 只有这一笔订单,所有匹配出来的粉丝登记都删除
                    remoteVermicelliService.removeExtendVermicelliByIds(vermicelliList.stream().map(ExtendVermicelli::getId).toList(), SecurityConstants.INNER).getData();
                } else {
                    int markers = 0;
                    int markers1 = 0;
                    List<Long> removeVermicelliList = CollUtil.newArrayList();  // 要删除的粉丝登记ID集

                    // 总充值
                    BigDecimal money = orderMapper.calculationExtendTotalMoney(customerIds, userVo.getPdUserId());
                    LocalDate minOrderTime = orderList.stream().min(Comparator.comparing(CrmCustomerOrder::getOrderTime)).orElse(null).getOrderTime().toLocalDate();
                    BigDecimal orderTotalMoney = orderList.stream().map(CrmCustomerOrder::getOrderMoney).reduce(BigDecimal.ZERO, BigDecimal::add);      // 一个月的充值数

                    for (ExtendVermicelli v : vermicelliList) {
                        if (v.getFansType() == 5) {    // 首充
                            if (ObjUtil.isEmpty(minOrderTime)) {
                                markers1++;
                                removeVermicelliList.add(v.getId());
                            } else if (v.getRecordDate().compareTo(minOrderTime) != 0) {
                                // 首充达成时间和订单第一条的时间不是同一天就修改
                                v.setRecordDate(minOrderTime);
                                markers++;
                            }

                        } else if (v.getFansType() == 0) {   // 两百粉
                            // 按天分组并累加 orderMoney
                            Map<LocalDate, BigDecimal> dailyOrderMoneyMap = orderList.stream()
                                    .collect(Collectors.groupingBy(
                                            order -> order.getOrderTime().toLocalDate(), // 按天分组
                                            Collectors.reducing(BigDecimal.ZERO, order -> order.getOrderMoney(), BigDecimal::add) // 累加 orderMoney
                                    ));
                            LocalDate firstGreaterThan200 = dailyOrderMoneyMap.entrySet().stream()
                                    .sorted(Map.Entry.comparingByKey())         // 排序
                                    .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(200)) >= 0)
                                    .map(Map.Entry::getKey)
                                    .findFirst()
                                    .orElse(null);

                            if (ObjUtil.isEmpty(firstGreaterThan200)) {
                                markers1++;
                                removeVermicelliList.add(v.getId());
                            } else if (v.getRecordDate().compareTo(firstGreaterThan200) != 0) {
                                v.setRecordDate(firstGreaterThan200);
                                markers++;
                            }

                        } else if (v.getFansType() == 1) {   // 五百粉
                            // 按天分组并累加 orderMoney
                            Map<LocalDate, BigDecimal> dailyOrderMoneyMap = orderList.stream()
                                    .collect(Collectors.groupingBy(
                                            order -> order.getOrderTime().toLocalDate(), // 按天分组
                                            Collectors.reducing(BigDecimal.ZERO, order -> order.getOrderMoney(), BigDecimal::add) // 累加 orderMoney
                                    ));
                            LocalDate firstGreaterThan500 = dailyOrderMoneyMap.entrySet().stream()
                                    .sorted(Map.Entry.comparingByKey())         // 排序
                                    .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(500)) >= 0)
                                    .map(Map.Entry::getKey)
                                    .findFirst()
                                    .orElse(null);

                            if (ObjUtil.isEmpty(firstGreaterThan500)) {
                                markers1++;
                                removeVermicelliList.add(v.getId());
                            } else if (v.getRecordDate().compareTo(firstGreaterThan500) != 0) {
                                v.setRecordDate(firstGreaterThan500);
                                markers++;
                            }

                        } else if (v.getFansType() == 2) {   // 五千粉
                            if (orderTotalMoney.compareTo(BigDecimal.valueOf(5000)) >= 0) {
                                LocalDate recordDate = orderList.stream()
                                        .reduce(
                                                new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                                (acc, order) -> {
                                                    BigDecimal newAccumulated = acc.getKey().add(order.getOrderMoney());
                                                    if (newAccumulated.compareTo(BigDecimal.valueOf(5000)) >= 0) {    // 统计第一次累加到5千时的订单时间
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, order.getOrderTime());
                                                    } else {
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                    }
                                                },
                                                (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                        ).getValue().toLocalDate();

                                if (v.getRecordDate().compareTo(recordDate) != 0) {
                                    v.setRecordDate(recordDate);
                                    markers++;
                                }
                            } else {
                                markers1++;
                                removeVermicelliList.add(v.getId());
                            }

                        } else if (v.getFansType() == 3) {   // 五万粉
                            if (orderTotalMoney.compareTo(BigDecimal.valueOf(50000)) >= 0) {
                                LocalDate recordDate = orderList.stream()
                                        .reduce(
                                                new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                                (acc, order) -> {
                                                    BigDecimal newAccumulated = acc.getKey().add(order.getOrderMoney());
                                                    if (newAccumulated.compareTo(BigDecimal.valueOf(50000)) >= 0) {   // 统计第一次累加到5万时的订单时间
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, order.getOrderTime());
                                                    } else {
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                    }
                                                },
                                                (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                        ).getValue().toLocalDate();

                                if (v.getRecordDate().compareTo(recordDate) != 0) {
                                    v.setRecordDate(recordDate);
                                    markers++;
                                }
                            } else {
                                markers1++;
                                removeVermicelliList.add(v.getId());
                            }

                        } else if (v.getFansType() == 6) {
                            if (money.compareTo(BigDecimal.valueOf(100000)) < 0) {
                                markers1++;
                                removeVermicelliList.add(v.getId());
                            }
                        } else if (v.getFansType() == 7) {
                            if (money.compareTo(BigDecimal.valueOf(400000)) < 0) {
                                markers1++;
                                removeVermicelliList.add(v.getId());
                            }
                        } else if (v.getFansType() == 8) {
                            if (money.compareTo(BigDecimal.valueOf(1000000)) < 0) {
                                markers1++;
                                removeVermicelliList.add(v.getId());
                            }
                        }
                    }

                    if (markers > 0) {
                        remoteVermicelliService.updExtendVermicelliList(vermicelliList, SecurityConstants.INNER).getData();
                    }
                    if (markers1 > 0) {
                        remoteVermicelliService.removeExtendVermicelliByIds(removeVermicelliList, SecurityConstants.INNER).getData();
                    }
                }
            }
        }
    }

    /**
     * 判断VIP粉丝登记(新增)
     *
     * @param orderTime  订单发起时间
     * @param customerId 订单客户id
     */
    protected void judgmentVipVermicelliAdd(Long customerId, Long serveId, LocalDate orderTime) {
        List<SysUserVo> list = remoteUserService.getUserList(UserQuery.builder()
                .pdUserId(CollUtil.newArrayList(serveId)).build(), SecurityConstants.INNER).getData();
        SysUserVo serveUser = new SysUserVo();
        if (CollUtil.isNotEmpty(list)) {
            serveUser = list.get(0);
        }

        if (StrUtil.isNotEmpty(serveUser.getNickName())) {
            // 查出此客户的关联好友
            CrmCustomerFriend friendCustomer = crmCustomerFriendMapper.selectOne(new LambdaQueryWrapper<CrmCustomerFriend>()
                    .eq(CrmCustomerFriend::getCustomerId, customerId)
                    .eq(CrmCustomerFriend::getStatus, 0)
                    .apply("end_time IS NULL")
                    .last("limit 1"));

            if (ObjUtil.isNotNull(friendCustomer)) {        // 客户没有绑定好友不进行粉丝登记
                // 查询好友下绑定的客户
                List<CrmCustomerFriend> customerFriends = crmCustomerFriendMapper.selectList(new LambdaQueryWrapper<CrmCustomerFriend>()
                        .in(CrmCustomerFriend::getFriendId, friendCustomer.getFriendId())
                        .eq(CrmCustomerFriend::getStatus, 0)
                        .apply("end_time IS NULL"));

                // 好友已录入的粉丝登记
                List<VipVermicelli> vipVermicelliList = remoteVermicelliService.selVipVermicelliList(
                        CollUtil.newArrayList(serveUser.getUserId()),
                        null,
                        CollUtil.newArrayList(friendCustomer.getFriendId()),
                        null,
                        null,
                        SecurityConstants.INNER).getData();

                // 获取传入时间的这个月(如果有达成记录就)的订单(并且是属于这个好友下的客户集的订单)已完成订单
                List<CrmCustomerOrder> orderList = customerOrderService.list(new LambdaQueryWrapper<CrmCustomerOrder>()
                        .in(CrmCustomerOrder::getCustomerId, customerFriends.stream().map(CrmCustomerFriend::getCustomerId).distinct().toList())
                        .in(CrmCustomerOrder::getPyServeId, serveId)    // 好友领取人绑定的PYID集
                        .eq(CrmCustomerOrder::getOrderStatus, 1)     // 已完成
                        .ge(CollUtil.isNotEmpty(vipVermicelliList), CrmCustomerOrder::getOrderTime, orderTime.withDayOfMonth(1))// 月初时间
                        .orderByAsc(CrmCustomerOrder::getOrderTime));    // 订单排序

                if (CollUtil.isNotEmpty(orderList)) {

                    SysUserVo extendUserVo = new SysUserVo();
                    List<SysUserVo> userVoList = remoteUserService.getUserList(UserQuery.builder()
                            .pdUserId(CollUtil.newArrayList(friendCustomer.getPyExtendId()))
                            .build(), SecurityConstants.INNER).getData();

                    if (CollUtil.isNotEmpty(userVoList)) {
                        extendUserVo = userVoList.get(0);
                    }

                    // VIP粉丝登记模版
                    VipVermicelli vermicelli = new VipVermicelli();
                    vermicelli.setFriendId(friendCustomer.getFriendId());
                    vermicelli.setCustomerIds(customerFriends.stream().map(CrmCustomerFriend::getCustomerId).distinct().toList()
                            .stream().map(String::valueOf).collect(Collectors.joining(",")));
                    vermicelli.setExtendId(extendUserVo.getUserId());
                    vermicelli.setExtendDeptId(extendUserVo.getDeptId());
                    vermicelli.setServeId(serveUser.getUserId());
                    vermicelli.setServeDeptId(serveUser.getDeptId());
                    vermicelli.setCreateTime(LocalDateTime.now());
                    vermicelli.setRemark("充值订单产生粉丝登记");
                    vermicelli.setCreateBy(1L);

                    boolean blsc = vipVermicelliList.stream().count() == 0;
                    boolean bl200 = vipVermicelliList.stream().filter(e -> e.getFansType() == 0).count() == 0;
                    boolean bl500 = vipVermicelliList.stream().filter(e -> e.getFansType() == 1).count() == 0;

                    // 查询客户的二交时间(早于七月,并且以前符合可以达成200粉的条件)
                    CrmCustomerJoinServe joinServe = customerJoinServeMapper.selectList(new LambdaQueryWrapper<CrmCustomerJoinServe>()
                            .eq(CrmCustomerJoinServe::getCustomerId, customerId)
                            .eq(CrmCustomerJoinServe::getStatus, 1)         // 已交接
                            .last("limit 1")).stream().limit(1).findFirst().orElse(null);

                    List<CrmCustomerOrder> joinOrderList = customerOrderService.list(new LambdaQueryWrapper<CrmCustomerOrder>()
                            .in(CrmCustomerOrder::getCustomerId, customerFriends.stream().map(CrmCustomerFriend::getCustomerId).distinct().toList())
                            .in(CrmCustomerOrder::getPyServeId, serveId)    // 好友领取人绑定的PYID集
                            .eq(CrmCustomerOrder::getOrderStatus, 1)     // 已完成
                            .orderByAsc(CrmCustomerOrder::getOrderTime));    // 订单排序

                    boolean joinServeStatus = false;  // true:早于七月 false:不用管
                    LocalDate julyFirstThisYear = LocalDate.of(2025, Month.JULY, 1);
                    if (ObjUtil.isNotNull(joinServe) &&                                         // 二交记录不能为空
                            joinServe.getJoinTime().toLocalDate().isBefore(julyFirstThisYear) &&    // 交接时间不能早于七月
                            orderList.size() != joinOrderList.size()) {                             // 这个月的订单不能等于总订单
                        joinServeStatus = true;
                    }

                    if (blsc) {
                        vermicelli.setFansType(5);
                        vermicelli.setRecordDate(orderList.stream().map(CrmCustomerOrder::getOrderTime).min(LocalDateTime::compareTo).get().toLocalDate());

                        if (joinServeStatus) {
                            // 看以前是否有符合条件的首充订单存在
                            if (joinOrderList.stream().map(CrmCustomerOrder::getOrderTime).min(LocalDateTime::compareTo).orElse(null)
                                    .compareTo(orderList.stream().map(CrmCustomerOrder::getOrderTime).min(LocalDateTime::compareTo).orElse(null)) != 0) {
                                // 两个订单集的第一条订单不是同一时间把达成时间改到2000年1月1号
                                vermicelli.setRecordDate(LocalDate.of(2000, 1, 1));
                            }
                        }
                        remoteVermicelliService.addVipVermicelli(vermicelli, SecurityConstants.INNER);
                    }

                    if (bl200 || bl500) {
                        // 按天分组并累加 orderMoney
                        Map<LocalDate, BigDecimal> dailyOrderMoneyMap = orderList.stream()
                                .collect(Collectors.groupingBy(
                                        order -> order.getOrderTime().toLocalDate(), // 按天分组
                                        Collectors.reducing(BigDecimal.ZERO, order -> order.getOrderMoney(), BigDecimal::add) // 累加 orderMoney
                                ));
                        if (bl200) {
                            LocalDate firstGreaterThan200 = dailyOrderMoneyMap.entrySet().stream()
                                    .sorted(Map.Entry.comparingByKey())         // 排序
                                    .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(200)) >= 0)
                                    .map(Map.Entry::getKey)
                                    .findFirst()
                                    .orElse(null);

                            if (ObjUtil.isNotNull(firstGreaterThan200)) {
                                vermicelli.setFansType(0);
                                vermicelli.setRecordDate(firstGreaterThan200);

                                if (joinServeStatus) {
                                    // 看以前是否有符合条件的200粉订单存在
                                    dailyOrderMoneyMap = joinOrderList.stream()
                                            .collect(Collectors.groupingBy(
                                                    order -> order.getOrderTime().toLocalDate(), // 按天分组
                                                    Collectors.reducing(BigDecimal.ZERO, order -> order.getOrderMoney(), BigDecimal::add) // 累加 orderMoney
                                            ));
                                    LocalDate beforeTime = dailyOrderMoneyMap.entrySet().stream()
                                            .sorted(Map.Entry.comparingByKey())         // 排序
                                            .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(200)) >= 0)
                                            .map(Map.Entry::getKey)
                                            .findFirst()
                                            .orElse(null);
                                    if (beforeTime.compareTo(firstGreaterThan200) != 0) {
                                        // 两个订单集的首充达成200粉时间不是同一时间把达成时间改到2000年1月1号
                                        vermicelli.setRecordDate(LocalDate.of(2000, 1, 1));
                                    }
                                }
                                remoteVermicelliService.addVipVermicelli(vermicelli, SecurityConstants.INNER);
                            }

                        }
                        if (bl500) {
                            LocalDate firstGreaterThan500 = dailyOrderMoneyMap.entrySet().stream()
                                    .sorted(Map.Entry.comparingByKey())         // 排序
                                    .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(500)) >= 0)
                                    .map(Map.Entry::getKey)
                                    .findFirst()
                                    .orElse(null);

                            if (ObjUtil.isNotNull(firstGreaterThan500)) {
                                vermicelli.setFansType(1);
                                vermicelli.setRecordDate(firstGreaterThan500);

                                if (joinServeStatus) {
                                    // 看以前是否有符合条件的500粉订单存在
                                    dailyOrderMoneyMap = joinOrderList.stream()
                                            .collect(Collectors.groupingBy(
                                                    order -> order.getOrderTime().toLocalDate(), // 按天分组
                                                    Collectors.reducing(BigDecimal.ZERO, order -> order.getOrderMoney(), BigDecimal::add) // 累加 orderMoney
                                            ));
                                    LocalDate beforeTime = dailyOrderMoneyMap.entrySet().stream()
                                            .sorted(Map.Entry.comparingByKey())         // 排序
                                            .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(500)) >= 0)
                                            .map(Map.Entry::getKey)
                                            .findFirst()
                                            .orElse(null);
                                    if (beforeTime.compareTo(firstGreaterThan500) != 0) {
                                        // 两个订单集的首充达成200粉时间不是同一时间把达成时间改到2000年1月1号
                                        vermicelli.setRecordDate(LocalDate.of(2000, 1, 1));
                                    }
                                }
                                remoteVermicelliService.addVipVermicelli(vermicelli, SecurityConstants.INNER);
                            }
                        }

                    }


                    boolean bl5k = vipVermicelliList.stream().filter(e -> e.getFansType() == 2
                            && e.getRecordDate().getMonthValue() == orderTime.getMonthValue()
                            && e.getRecordDate().getYear() == orderTime.getYear()).count() == 0;
                    boolean bl5w = vipVermicelliList.stream().filter(e -> e.getFansType() == 3
                            && e.getRecordDate().getMonthValue() == orderTime.getMonthValue()
                            && e.getRecordDate().getYear() == orderTime.getYear()).count() == 0;

                    if (bl5k || bl5w) {
                        boolean bl5kOlb = vipVermicelliList.stream().filter(v -> v.getFansType() == 2).count() == 0;
                        boolean bl5wOlb = vipVermicelliList.stream().filter(v -> v.getFansType() == 3).count() == 0;

                        BigDecimal monthUp = orderList.stream().map(CrmCustomerOrder::getOrderMoney).reduce(BigDecimal.ZERO, BigDecimal::add);      // 一个月的充值数

                        if (monthUp.compareTo(BigDecimal.valueOf(5000)) >= 0 && bl5k) {
                            vermicelli.setFansType(2);
                            vermicelli.setStatus(bl5kOlb ? "0" : "1");
                            vermicelli.setRecordDate(orderList.stream()
                                    .reduce(
                                            new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                            (acc, order) -> {
                                                BigDecimal newAccumulated = acc.getKey().add(order.getOrderMoney());
                                                if (newAccumulated.compareTo(BigDecimal.valueOf(5000)) >= 0) {    // 统计第一次累加到5千时的订单时间
                                                    return new AbstractMap.SimpleEntry<>(newAccumulated, order.getOrderTime());
                                                } else {
                                                    return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                }
                                            },
                                            (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                    ).getValue().toLocalDate());
                            remoteVermicelliService.addVipVermicelli(vermicelli, SecurityConstants.INNER);
                        }
                        if (monthUp.compareTo(BigDecimal.valueOf(50000)) >= 0 && bl5w) {
                            vermicelli.setFansType(3);
                            vermicelli.setStatus(bl5wOlb ? "0" : "1");
                            vermicelli.setRecordDate(orderList.stream()
                                    .reduce(
                                            new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                            (acc, order) -> {
                                                BigDecimal newAccumulated = acc.getKey().add(order.getOrderMoney());
                                                if (newAccumulated.compareTo(BigDecimal.valueOf(50000)) >= 0) {   // 统计第一次累加到5万时的订单时间
                                                    return new AbstractMap.SimpleEntry<>(newAccumulated, order.getOrderTime());
                                                } else {
                                                    return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                }
                                            },
                                            (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                    ).getValue().toLocalDate());
                            remoteVermicelliService.addVipVermicelli(vermicelli, SecurityConstants.INNER);
                        }
                    }

                    BigDecimal money = orderMapper.calculationVipTotalMoney(customerFriends.stream().map(CrmCustomerFriend::getCustomerId).distinct().toList(), CollUtil.newArrayList(serveId));

                    boolean bl10w = vipVermicelliList.stream().filter(e -> e.getFansType() == 6).count() == 0;
                    boolean bl40w = vipVermicelliList.stream().filter(e -> e.getFansType() == 7).count() == 0;
                    boolean bl100w = vipVermicelliList.stream().filter(e -> e.getFansType() == 8).count() == 0;

                    if (bl10w && money.compareTo(BigDecimal.valueOf(100000)) >= 0) {
                        vermicelli.setFansType(6);
                        vermicelli.setRecordDate(LocalDate.now());
                        remoteVermicelliService.addVipVermicelli(vermicelli, SecurityConstants.INNER);
                    } else if (bl40w && money.compareTo(BigDecimal.valueOf(400000)) >= 0) {
                        vermicelli.setFansType(7);
                        vermicelli.setRecordDate(LocalDate.now());
                        remoteVermicelliService.addVipVermicelli(vermicelli, SecurityConstants.INNER);
                    } else if (bl100w && money.compareTo(BigDecimal.valueOf(1000000)) >= 0) {
                        vermicelli.setFansType(8);
                        vermicelli.setRecordDate(LocalDate.now());
                        remoteVermicelliService.addVipVermicelli(vermicelli, SecurityConstants.INNER);
                    }
                }
            }
        }
    }

    /**
     * 判断VIP粉丝登记(删除/修改)
     *
     * @param orderTime  订单发起时间
     * @param customerId 订单客户id
     * @param pdServeId  pd客服id
     */
    protected void judgmentVipVermicelliDelete(Long pdServeId, Long customerId, LocalDate orderTime) {
        List<SysUserVo> userVoList = remoteUserService.getUserList(UserQuery.builder()
                .pdUserId(CollUtil.newArrayList(pdServeId))
                .build(), SecurityConstants.INNER).getData();
        if (CollUtil.isNotEmpty(userVoList)) {
            if (userVoList.size() != 1) {
                throw new ServiceException("充值订单业务处理异常:[" + pdServeId + "]存在多个OA用户绑定!");
            }
            SysUserVo serveUser = userVoList.get(0);
            List<VipVermicelli> vermicelliList = remoteVermicelliService.selOrderVipVermicelliList(serveUser.getUserId(), customerId, orderTime.toString(), SecurityConstants.INNER).getData();

            if (CollUtil.isNotEmpty(vermicelliList)) {
                List<Long> customerIds = vermicelliList.stream()
                        .flatMap(v ->
                                Arrays.stream(v.getCustomerIds().split(","))
                        )
                        .map(Long::parseLong)
                        .distinct()
                        .collect(Collectors.toList());

                // 获取传入时间的这个月的订单(并且是属于这个好友下的客户集的订单)已完成订单
                List<CrmCustomerOrder> orderList = customerOrderService.list(new LambdaQueryWrapper<CrmCustomerOrder>()
                        .in(CrmCustomerOrder::getCustomerId, customerIds)
                        .in(CrmCustomerOrder::getPyServeId, pdServeId)    // 以前达成的粉丝登记的VIP归属
                        .eq(CrmCustomerOrder::getOrderStatus, 1)     // 已完成
                        .ge(CrmCustomerOrder::getOrderTime, orderTime.withDayOfMonth(1))// 月初时间
                        .orderByAsc(CrmCustomerOrder::getOrderTime));    // 订单排序

                if (CollUtil.isEmpty(orderList)) {  // 只有这一笔订单,所有匹配出来的粉丝登记都删除
                    remoteVermicelliService.removeVipVermicelliByIds(vermicelliList.stream().map(VipVermicelli::getId).toList(), SecurityConstants.INNER).getData();
                } else {
                    int markers = 0;
                    int markers1 = 0;
                    List<Long> removeVermicelliList = CollUtil.newArrayList();  // 要删除的粉丝登记ID集

                    LocalDate minOrderTime = orderList.stream().min(Comparator.comparing(CrmCustomerOrder::getOrderTime)).orElse(null).getOrderTime().toLocalDate();
                    BigDecimal monthUp = orderList.stream().map(CrmCustomerOrder::getOrderMoney).reduce(BigDecimal.ZERO, BigDecimal::add);      // 一个月的充值数
                    BigDecimal money = orderMapper.calculationVipTotalMoney(customerIds, CollUtil.newArrayList(pdServeId));
                    for (VipVermicelli v : vermicelliList) {
                        if (v.getFansType() == 5) {    // 首充
                            if (ObjUtil.isEmpty(minOrderTime)) {
                                markers1++;
                                removeVermicelliList.add(v.getId());
                            } else if (v.getRecordDate().compareTo(minOrderTime) != 0) {
                                // 首充达成时间和订单第一条的时间不是同一天就修改
                                v.setRecordDate(minOrderTime);
                                markers++;
                            }

                        } else if (v.getFansType() == 0) {   // 两百粉
                            // 按天分组并累加 orderMoney
                            Map<LocalDate, BigDecimal> dailyOrderMoneyMap = orderList.stream()
                                    .collect(Collectors.groupingBy(
                                            order -> order.getOrderTime().toLocalDate(), // 按天分组
                                            Collectors.reducing(BigDecimal.ZERO, order -> order.getOrderMoney(), BigDecimal::add) // 累加 orderMoney
                                    ));
                            LocalDate firstGreaterThan200 = dailyOrderMoneyMap.entrySet().stream()
                                    .sorted(Map.Entry.comparingByKey())         // 排序
                                    .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(200)) >= 0)
                                    .map(Map.Entry::getKey)
                                    .findFirst()
                                    .orElse(null);

                            if (ObjUtil.isEmpty(firstGreaterThan200)) {
                                markers1++;
                                removeVermicelliList.add(v.getId());
                            } else if (v.getRecordDate().compareTo(firstGreaterThan200) != 0) {
                                v.setRecordDate(firstGreaterThan200);
                                markers++;
                            }

                        } else if (v.getFansType() == 1) {   // 五百粉
                            // 按天分组并累加 orderMoney
                            Map<LocalDate, BigDecimal> dailyOrderMoneyMap = orderList.stream()
                                    .collect(Collectors.groupingBy(
                                            order -> order.getOrderTime().toLocalDate(), // 按天分组
                                            Collectors.reducing(BigDecimal.ZERO, order -> order.getOrderMoney(), BigDecimal::add) // 累加 orderMoney
                                    ));
                            LocalDate firstGreaterThan500 = dailyOrderMoneyMap.entrySet().stream()
                                    .sorted(Map.Entry.comparingByKey())         // 排序
                                    .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(500)) >= 0)
                                    .map(Map.Entry::getKey)
                                    .findFirst()
                                    .orElse(null);

                            if (ObjUtil.isEmpty(firstGreaterThan500)) {
                                markers1++;
                                removeVermicelliList.add(v.getId());
                            } else if (v.getRecordDate().compareTo(firstGreaterThan500) != 0) {
                                v.setRecordDate(firstGreaterThan500);
                                markers++;
                            }

                        } else if (v.getFansType() == 2) {   // 五千粉
                            if (monthUp.compareTo(BigDecimal.valueOf(5000)) >= 0) {
                                LocalDate recordDate = orderList.stream()
                                        .reduce(
                                                new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                                (acc, order) -> {
                                                    BigDecimal newAccumulated = acc.getKey().add(order.getOrderMoney());
                                                    if (newAccumulated.compareTo(BigDecimal.valueOf(5000)) >= 0) {    // 统计第一次累加到5千时的订单时间
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, order.getOrderTime());
                                                    } else {
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                    }
                                                },
                                                (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                        ).getValue().toLocalDate();

                                if (v.getRecordDate().compareTo(recordDate) != 0) {
                                    v.setRecordDate(recordDate);
                                    markers++;
                                }
                            } else {
                                markers1++;
                                removeVermicelliList.add(v.getId());
                            }

                        } else if (v.getFansType() == 3) {   // 五万粉
                            if (monthUp.compareTo(BigDecimal.valueOf(50000)) >= 0) {
                                LocalDate recordDate = orderList.stream()
                                        .reduce(
                                                new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                                (acc, order) -> {
                                                    BigDecimal newAccumulated = acc.getKey().add(order.getOrderMoney());
                                                    if (newAccumulated.compareTo(BigDecimal.valueOf(50000)) >= 0) {  // 统计第一次累加到5万时的订单时间
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, order.getOrderTime());
                                                    } else {
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                    }
                                                },
                                                (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                        ).getValue().toLocalDate();

                                if (v.getRecordDate().compareTo(recordDate) != 0) {
                                    v.setRecordDate(recordDate);
                                    markers++;
                                }
                            } else {
                                markers1++;
                                removeVermicelliList.add(v.getId());
                            }

                        } else if (v.getFansType() == 6) {
                            if (money.compareTo(BigDecimal.valueOf(100000)) < 0) {
                                markers1++;
                                removeVermicelliList.add(v.getId());
                            }
                        } else if (v.getFansType() == 7) {
                            if (money.compareTo(BigDecimal.valueOf(400000)) < 0) {
                                markers1++;
                                removeVermicelliList.add(v.getId());
                            }
                        } else if (v.getFansType() == 8) {
                            if (money.compareTo(BigDecimal.valueOf(1000000)) < 0) {
                                markers1++;
                                removeVermicelliList.add(v.getId());
                            }
                        }
                    }

                    if (markers > 0) {
                        remoteVermicelliService.updVipVermicelliList(vermicelliList, SecurityConstants.INNER).getData();
                    }
                    if (markers1 > 0) {
                        remoteVermicelliService.removeVipVermicelliByIds(removeVermicelliList, SecurityConstants.INNER).getData();
                    }
                }
            }
        }
    }

    /**
     * 判断运营首充
     */
    protected void operateFirstCharge(Long customerId, LocalDateTime orderTime) {
        CrmCustomerJoinAnchor joinAnchor = customerJoinAnchorMapper.selectOne(new LambdaQueryWrapper<CrmCustomerJoinAnchor>()    // 查询一交信息
                .eq(CrmCustomerJoinAnchor::getCustomerId, customerId)
                .eq(CrmCustomerJoinAnchor::getStatus, 1)
                .last("limit 1"));      // 已交接

        // 查询是否有大号或小号已经达成过了粉丝登记
        List<OperateVermicelli> vermicelliList = remoteVermicelliService.selOperateVermicelliList(null, 7, customerId, null, null, null, SecurityConstants.INNER).getData();

        // 没有达成过粉丝登记
        if (CollUtil.isEmpty(vermicelliList) && ObjUtil.isNotNull(joinAnchor)) {
            // 查找一交接手时间到此订单区间内有多少订单
            List<CrmCustomerOrder> orderList = orderMapper.selectList(new LambdaQueryWrapper<CrmCustomerOrder>()
                    .eq(CrmCustomerOrder::getCustomerId, customerId)
                    .ge(CrmCustomerOrder::getOrderTime, joinAnchor.getJoinTime())
                    .le(CrmCustomerOrder::getOrderTime, orderTime));

            if (CollUtil.isNotEmpty(orderList)) {
                // 拿到最早那条订单的时间
                orderTime = orderList.stream().min(Comparator.comparing(CrmCustomerOrder::getOrderTime)).orElse(null).getOrderTime();

                // 开始时间：如果订单时间减三个月 小于 一交接手时间那取一交接手时间反而取订单时间减三个月
                LocalDateTime beginTime = orderTime.minusMonths(3).isAfter(joinAnchor.getJoinTime()) ? orderTime.minusMonths(3) : joinAnchor.getJoinTime();
                // 结束时间：订单时间
                LocalDateTime endTime = orderTime;

                List<OperateVermicelli> operateVermicelliList = customerJoinAnchorMapper.joinAnchorOperateVermicelli(customerId, beginTime, endTime);

                LocalDateTime finalOrderTime = orderTime;
                operateVermicelliList.stream().forEach(o -> {
                    o.setFansType(7);                               // 首充
                    o.setRecordDate(finalOrderTime.toLocalDate());  // 达成时间
                    o.setCreateTime(LocalDateTime.now());
                    o.setCreateBy(1L);
                    o.setRemark("充值表产生粉丝登记");

                    remoteVermicelliService.addOperateVermicelli(o, SecurityConstants.INNER);         // 新增运营的首充粉丝登记
                });
            }
        }
    }
}
