package com.yooa.crm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.crm.api.domain.CrmFriendArea;
import com.yooa.crm.mapper.CrmFriendAreaMapper;
import com.yooa.crm.service.FriendAreaService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 好友地区 - 服务实现层
 */
@Service
@AllArgsConstructor
public class FriendAreaServiceImpl extends ServiceImpl<CrmFriendAreaMapper, CrmFriendArea> implements FriendAreaService {

    @Override
    public List<CrmFriendArea> selectFriendAreaList(CrmFriendArea friendArea) {
        LambdaQueryWrapper<CrmFriendArea> queryWrapper = new LambdaQueryWrapper<>();
        
        if (ObjUtil.isNotNull(friendArea.getParentId())) {
            queryWrapper.eq(CrmFriendArea::getParentId, friendArea.getParentId());
        }
        if (StrUtil.isNotBlank(friendArea.getArea())) {
            queryWrapper.like(CrmFriendArea::getArea, friendArea.getArea());
        }
        
        queryWrapper.orderByAsc(CrmFriendArea::getSort);
        return list(queryWrapper);
    }

    @Override
    public CrmFriendArea selectFriendAreaById(Long id) {
        return getById(id);
    }

    @Override
    public int insertFriendArea(CrmFriendArea friendArea) {
        return save(friendArea) ? 1 : 0;
    }

    @Override
    public int updateFriendArea(CrmFriendArea friendArea) {
        return updateById(friendArea) ? 1 : 0;
    }

    @Override
    public int deleteFriendAreaByIds(Long[] ids) {
        return removeByIds(CollUtil.toList(ids)) ? ids.length : 0;
    }

    @Override
    public int deleteFriendAreaById(Long id) {
        return removeById(id) ? 1 : 0;
    }



    @Override
    public boolean checkAreaNameUnique(CrmFriendArea friendArea) {
        Long areaId = ObjUtil.isNull(friendArea.getId()) ? -1L : friendArea.getId();
        CrmFriendArea info = getOne(new LambdaQueryWrapper<CrmFriendArea>()
                .eq(CrmFriendArea::getArea, friendArea.getArea())
                .eq(CrmFriendArea::getParentId, friendArea.getParentId())
                .last("limit 1"));
        
        if (ObjUtil.isNotNull(info) && !info.getId().equals(areaId)) {
            return false;
        }
        return true;
    }

    @Override
    public boolean hasChildByAreaId(Long areaId) {
        long count = count(new LambdaQueryWrapper<CrmFriendArea>()
                .eq(CrmFriendArea::getParentId, areaId));
        return count > 0;
    }


}
