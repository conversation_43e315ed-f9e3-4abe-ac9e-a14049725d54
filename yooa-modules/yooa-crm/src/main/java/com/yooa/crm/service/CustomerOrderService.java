package com.yooa.crm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.crm.api.domain.CrmCustomerOrder;
import com.yooa.crm.api.domain.dto.OrderUpMessageDto;
import com.yooa.crm.api.domain.query.CustomerRewardQuery;
import com.yooa.crm.api.domain.query.RechargeRecordPitcherQuery;
import com.yooa.crm.api.domain.vo.CustomerOrderVo;
import com.yooa.crm.api.domain.vo.CustomerRewardInfoVo;
import com.yooa.crm.api.domain.vo.CustomerRewardVo;
import com.yooa.crm.api.domain.vo.ManufactureUp;
import com.yooa.crm.api.domain.vo.RechargeRecordPitcherVo;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 订单 - 业务层
 */
public interface CustomerOrderService extends IService<CrmCustomerOrder> {

    OrderUpMessageDto selRechargeRecord(List<Long> customerIds, Long userId);

    // 生成几百粉记录
    ManufactureUp manufactureUp(List<Long> extendIds, List<Long> customerIds, List<CrmCustomerOrder> orderList);

    /**
     * 获取昨天的订单数据
     */
    List<CustomerOrderVo> getOrderListByCompleteDate(LocalDate completeDate);

    // 查询每天的订单
    List<CrmCustomerOrder> selOrderDayMoney(List<Long> ids, String beginTime, String endTime, int type);

    // 查询新增业绩的每天的订单
    List<CrmCustomerOrder> selOrderDayNewMoney(List<Long> ids, String beginTime, String endTime, int type);

    CrmCustomerOrder getCrmCustomerOrderByOrderId(String orderId);

    /**
     * 客户打赏记录
     */
    CustomerRewardVo getRewardRecord(Page<CustomerRewardVo> page, CustomerRewardQuery customerRewardQuery);

    /**
     * 客户打赏记录 (所有客户)
     */
    CustomerRewardVo getRewardRecordList(Page<CustomerRewardVo> page, CustomerRewardQuery customerRewardQuery);

    /**
     * 客户打赏记录 (所有客户) 导出
     */
    List<CustomerRewardInfoVo> getRewardRecordExportList(Page page, CustomerRewardQuery query);



    /**
     * 投放 - 充值记录
     * @param page
     * @param query
     * @return
     */
    List<RechargeRecordPitcherVo> selRechargeRecordPitcher(Page page, RechargeRecordPitcherQuery query);

    /**
     * 投放 - 充值记录 - 统计
     * @param query
     * @return
     */
    Map<String,Object> selRechargeRecordPitcherStatistics(RechargeRecordPitcherQuery query);

    /**
     * 运营 - 充值记录
     * @param page
     * @param query
     * @return
     */
    List<RechargeRecordPitcherVo> selRechargeRecordPitcherForOperate(Page page, RechargeRecordPitcherQuery query);

    /**
     * 运营 - 充值记录 - 统计
     * @param query
     * @return
     */
    Map<String,Object> selRechargeRecordPitcherStatisticsForOperate(RechargeRecordPitcherQuery query);
}
