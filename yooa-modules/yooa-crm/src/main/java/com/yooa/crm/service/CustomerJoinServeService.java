package com.yooa.crm.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.crm.api.domain.CrmCustomerJoinServe;
import com.yooa.crm.api.domain.query.CustomerJoinServePitcherQuery;
import com.yooa.crm.api.domain.query.CustomerJoinServeQuery;
import com.yooa.crm.api.domain.vo.CustomerJoinServeVo;

import java.util.List;

/**
 * 客户客服交接 - 服务层
 */
public interface CustomerJoinServeService extends IService<CrmCustomerJoinServe> {

    /**
     * 流失时间/过期时间校正
     */
    public void correctionExpireTime();

    List<CustomerJoinServeVo> getList(Page page, CustomerJoinServeQuery query);

    /**
     *  投放二交列表
     * @param page
     * @param query
     * @return
     */
    List pitcherList(Page page, CustomerJoinServePitcherQuery query);
}
