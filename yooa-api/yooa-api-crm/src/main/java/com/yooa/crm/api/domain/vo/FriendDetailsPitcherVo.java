package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import com.yooa.crm.api.domain.CrmFriend;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 好友信息详情类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FriendDetailsPitcherVo extends CrmFriend implements Serializable {

    /**
     * 一级渠道名称
     */
    @Excel(name = "主渠道名")
    private String mainChannelName;

    /**
     * 二级渠道名称
     */
    @Excel(name = "子渠道名")
    private String subChannelName;

    /**
     * 渠道负责人/投手 名称
     */
    private String channelUserName;

    /**
     * 渠道负责人/投手 昵称
     */
    @Excel(name = "投手")
    private String channelNickName;

    /**
     * 领取人 名称
     */
    private String receiveUserName;

    /**
     * 领取人 昵称
     */
    @Excel(name = "推广")
    private String receiveNickName;

    /**
     * 领取人 部门ID
     */
    private Long receiveDeptId;

    /**
     * 领取人 部门名
     */
    @Excel(name = "推广部")
    private String receiveDeptName;

    /**
     * 领取人 部门上级名拼接
     */
    private String receiveAncestorsNames;

    /**
     * 部门
     */
    @Excel(name = "推广部")
    private String teamName1;

    /**
     * 团队
     */
    @Excel(name = "推广团")
    private String teamName2;

    /**
     * 小组
     */
    @Excel(name = "推广组")
    private String teamName3;
}
