package com.yooa.crm.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/7/2 13:42
 * @Description:
 */
@Data
public class VipRechargeOrderQuery extends QueryEntity {

    /**
     * 充值最低金额
     */
  //  private BigDecimal minAmount;

    /**
     * 充值最高金额
     */
  //  private BigDecimal maxAmount;

    /**
     * 根据客户id 订单号 第三方订单号 查询
     */
    private String queryId;

    /**
     * 平台 1=pd,2=潘多拉,3=1v1,4=glowfun,5=poyo'
     */
    private String appProject;

    /**
     * 支付方式(字典)
     */
    private String paymentType;

    /**
     * 支付完成开始时间
     */
    private LocalDateTime paymentStartTime;

    /**
     * 支付完成结束时间
     */
    private LocalDateTime paymentEndTime;

    /**
     * 订单状态(1:已完成、2:已退款、3:特殊退款、4:更换投手)
     */
    private String status;

    /**
     * 推广部门id
     */
    private Long extendDeptId;

    /**
     * vip部门id
     */
    private Long serverDeptId;

    /**
     * 类型 1=首次下单 2=45天内下单
     */
    private Long type;

}
