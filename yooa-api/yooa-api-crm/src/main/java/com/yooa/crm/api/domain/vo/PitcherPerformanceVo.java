package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PitcherPerformanceVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 投手id
     */
    private Long pitcherId;

    /**
     * 投手用户昵称
     */
    @Excel(name = "投手")
    private String pitcherName;

    /**
     * 投手部门id
     */
    private Long pitcherDeptId;

    /**
     * 投手父部门名称集
     */
    @Excel(name = "投手父部门名称集")
    private String pitcherDeptNames;

    /**
     * 投手部门名称
     */
    @Excel(name = "投手部门名称")
    private String pitcherDeptName;


    /**
     * FB点击量
     */
    @Excel(name = "FB点击量")
    private Integer fbClick;

    /**
     * TK有效好友数
     */
    @Excel(name = "TK有效好友数")
    private Integer tkEffectiveFriend;

    /**
     * TK好友数
     */
    @Excel(name = "TK好友数")
    private Integer tkFriend;

    /**
     * 好友数
     */
    @Excel(name = "好友")
    private Integer friendNum;

    /**
     * 注册数
     */
    @Excel(name = "注册")
    private Integer customerNum;

    /**
     * 交接主播数
     */
    @Excel(name = "交接")
    private Integer handoverAnchorNum;

    /**
     * 交接客服数
     */
    @Excel(name = "二交")
    private Integer handoverServeNum;


    /**
     * 有效数
     */
    @Excel(name = "有效")
    private Integer firstChargeNum;

    /**
     * 业绩
     */
    @Excel(name = "业绩")
    private BigDecimal orderMoney;


    /**
     * 优质指标
     */
    @Excel(name = "优质指标")
    private Integer qualityNum;
}
