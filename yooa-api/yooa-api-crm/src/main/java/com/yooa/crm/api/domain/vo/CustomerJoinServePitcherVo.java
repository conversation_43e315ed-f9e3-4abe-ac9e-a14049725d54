package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/4/18 17:01
 * @Description:
 */
@Data
public class CustomerJoinServePitcherVo {

    /**
     * 客户id
     */
    @Excel(name = "客户id")
    private Long customerId;

    /**
     * 客户姓名
     */
    //@Excel(name = "客户姓名")
    private String customerName;

    /**
     * 昵称
     */
    @Excel(name = "昵称")
    private String friendName;

    /**
     * 大小号 0:大号 1:小号
     */
    @Excel(name = "大小号", readConverterExp = "0=大号,1=小号")
    private Integer accountType;

    /**
     * 接收时间
     */
    @Excel(name = "接收时间")
    private LocalDateTime receiveTime;

    /**
     * 推广姓名
     */
    @Excel(name = "推广姓名")
    private String extendUserName;

    /**
     * 推广id
     */
//    @Excel(name = "推广id")
//    private Long extendDeptId;

    /**
     * 推广组
     */
    @Excel(name = "推广组")
    private String extendDeptName;

    /**
     * 推广组级列表
     */
    @Excel(name = "推广组级列表")
    private String extendAncestors;

    /**
     * VIP姓名
     */
    @Excel(name = "VIP姓名")
    private String serveUserName;

    /**
     * VIPid
     */
//    @Excel(name = "VIPid")
//    private Long serveDeptId;

    /**
     * VIP组级列表
     */
    @Excel(name = "VIP组级列表")
    private String serveAncestors;

    /**
     * 语种
     */
    @Excel(name = "语种", readConverterExp = "1=中文,2=英文")
    private Integer language;

    /**
     * 投手
     */
    @Excel(name = "投手")
    private String channelNickName;

    /**
     * 渠道
     */
    @Excel(name = "渠道")
    private String channelName;

    /**
     * 粉丝类型（1男粉 2女粉 3英文粉 4中文粉）
     */
    @Excel(name = "粉丝类型", readConverterExp = "1=男粉,2=女粉,3=英文粉,4=中文粉")
    private String fansType;

    /**
     * 好友Id
     */
    private Long friendId;

    /**
     * 领取人id
     */
    private Long extendId;

    /**
     * 绑定状态 0:未绑定 1:已绑定
     */
    private Integer bindType;
}
