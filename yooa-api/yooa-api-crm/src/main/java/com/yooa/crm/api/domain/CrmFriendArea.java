package com.yooa.crm.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.core.annotation.Excel;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.*;

import java.util.List;

/**
 * 好友地区表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CrmFriendArea extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 地区名称
     */
    @Excel(name = "地区名称")
    private String area;

    /**
     * 父id
     */
    @Excel(name = "父id")
    private Long parentId;

    /**
     * 顺序
     */
    @Excel(name = "顺序")
    private Integer sort;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 子地区列表
     */
    @TableField(exist = false)
    private List<CrmFriendArea> children;
}
