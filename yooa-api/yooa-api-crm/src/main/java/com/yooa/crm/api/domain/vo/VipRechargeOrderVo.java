package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import com.yooa.common.core.annotation.ExcelDict;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/7/2 13:42
 * @Description:
 */
@Data
public class VipRechargeOrderVo {

    /**
     * 客户id
     */
    @Excel(name = "客户id")
    private Long customerId;

    /**
     * 订单号
     */
    @Excel(name = "订单号")
    private String orderNo;

    /**
     * 第三方订单号
     */
    @Excel(name = "第三方订单号")
    private String thirdOrderNo;

    /**
     * 创建时间
     */
  //  @Excel(name = "创建时间")
    private LocalDateTime orderTime;

    /**
     * 金额
     */
    @Excel(name = "金额")
    private BigDecimal amount;

    /**
     * 支付方式(字典)
     */
    @Excel(name = "支付方式")
    @ExcelDict(dictType = "payment_type")
    private String paymentType;

    /**
     * 客服名字
     */
    @Excel(name = "客服名字")
    private String serveName;

    /**
     * 客服部门名字
     */
   // @Excel(name = "客服部门名字")
   // private String serveDeptName;

    /**
     * 父部门
     */
  //  @Excel(name = "父部门")
  //  private String serveAncestorsNames;

    /**
     * 平台 1=pd,2=潘多拉,3=1v1,4=glowfun,5=poyo'
     */
    @Excel(name = "平台")
    @ExcelDict(dictType = "crm_order_project")
    private String appProject;

    /**
     * 昵称
     */
    @Excel(name = "昵称")
    private String customerName;

    /**
     * 客户账号
     */
    @Excel(name = "客户账号")
    private String customerAccount;

    /**
     * 推广名字
     */
    @Excel(name = "推广名字")
    private String extendName;

    /**
     * 订单状态(1:已完成、2:已退款、3:特殊退款、4:更换投手)
     */
    @Excel(name = "订单状态")
    @ExcelDict(dictType = "crm_order_status")
    private String orderStatus;

    /**
     * 支付时间
     */
    @Excel(name = "支付时间")
    private LocalDateTime paymentTime;
}
