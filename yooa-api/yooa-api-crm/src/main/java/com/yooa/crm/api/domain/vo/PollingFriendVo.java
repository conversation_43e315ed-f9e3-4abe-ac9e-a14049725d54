package com.yooa.crm.api.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.core.annotation.Excel;
import com.yooa.common.mybatis.base.BaseEntity;
import com.yooa.crm.api.domain.CrmFriend;
import lombok.*;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PollingFriendVo extends BaseEntity {

    /**
     * 好友ID
     */
    @TableId(value = "friend_id", type = IdType.AUTO)
    private Long friendId;

    @Excel(name = "客户昵称")
    private String friendName;

    @Excel(name = "聊天日期", dateFormat = "yyyy-MM-dd")
    private LocalDate recordDate;

    @Excel(name = "类型", readConverterExp = "1=有效,2=无效")
    private Long pollingType;

    @Excel(name = "客户ID")
    private Long customerId;

    private Long pitcherId;

    @Excel(name = "投手")
    private String pitcherName;

    @Excel(name = "推广")
    private String extendName;

    @Excel(name = "渠道")
    private String channelName;

    /**
     * api的联系方式
     */
    @Excel(name = "联系方式")
    private String pollingContactMode;

    /**
     * 好友的联系方式
     */
    @Excel(name = "第三方联系方式")
    private String friendContactMode;

    /**
     * 地区(字典)
     */
    @Excel(name = "地区", readConverterExp = "1=香港,2=台湾,3=新加坡,4=马来西亚,5=东南亚(非新加坡、马来),6=澳洲(澳大利亚新西兰),7=北美,8=南美,9=中东,10=欧洲,11=其他地区,12=日韩")
    private String areaName;

    /**
     * 工作类型(字典)
     */
    @Excel(name = "职业", readConverterExp = "1=白领(销售、管理、公务员等脑力型工作),2=蓝领(有固定月薪的技术型工作),3=劳务(有固定月薪的体力型工作)," +
            "4=服务业(固定月薪的服务型行业),5=短工(各种不稳定的临时工作),6=个体户(自己当老板),7=待业(失业),8=半工半读,9=学生,10=退休,11=自由职业(有稳定收入来源)")
    private String workTypeName;

    @Excel(name = "收入水平")
    private String incomeLevel;

    /**
     * 性别(字典)
     */
    private String sexName;

    /**
     * 婚姻状况(字典)
     */
    @Excel(name = "婚姻状况", readConverterExp = "1=未婚,2=已婚,3=其他")
    private String matrimonyName;

    /**
     * 语种(字典)
     */
    @Excel(name = "语种", readConverterExp = "1=汉语,2=英语")
    private String languageName;

    /**
     * 粉丝类型（1男粉 2女粉 3英文粉 4中文粉）
     */
    @Excel(name = "粉丝类型", readConverterExp = "1=男粉,2=女粉,3=英文粉,4=中文粉")
    private String fansTypeName;

    /**
     * 年龄
     */
    @Excel(name = "年龄")
    private Integer age;

    /**
     * 情感需求(字典)
     */
    @Excel(name = "情感需求", readConverterExp = "1=恋爱,2=交友,3=结婚,4=猎奇,5=陪伴,6=娱乐消遣,7=其他")
    private String demandName;

    @Excel(name = "轮询备注")
    private String remark;

    @Excel(name = "备注")
    private String reviewRemark;

    /**
     * api渠道名称
     */
    private String pollingApiChannelName;
    /**
     * api渠道电话
     */
    private String pollingApiPhone;

    /**
     * 联系方式名称
     */
    private String contactModeName;
    /**
     * 电话
     */
    private String contactPhone;

    /**
     * 主渠道名称
     */
    private String mainChanneName;

    /**
     * 子渠道名称
     */
    private String subChanneName;

    /**
     * 轮询api客户id
     */
    private String pollingApiUserId;
    /**
     * 状态
     */
    private Integer state;
}
