package com.yooa.crm.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 主播信息表
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CrmAnchorInfo extends BaseEntity {

    /**
     * 主播信息id
     */
    @TableId(type = IdType.AUTO)
    private Long anchorId;

    /**
     * 主播姓名
     */
    private String anchorName;

    /**
     * 主播花名
     */
    private String flowerName;

    /**
     * 面试时间
     */
    private LocalDate interviewDate;

    /**
     * 基本薪资
     */
    private BigDecimal baseSalary;

    /**
     * 邀约人id
     */
    private Long inviterId;

    /**
     * 初试人id
     */
    private Long firstRecruiterId;

    /**
     * 复试人id
     */
    private Long finalRecruiterId;

    /**
     * 状态（0待面试 1面试中 2初试未过 3待复试 4复试中 5复试未过 6待签约 7已签约 8已解约）
     */
    private String anchorStatus;

    /**
     * 主播风格（1搞笑 2幽默）
     */
    private String anchorStyle;

    /**
     * 主播类型（1线上 2线下 3兼职）
     */
    private String anchorType;

    /**
     * 地区
     */
    private String region;

    /**
     * 语言（1中文 2英文）
     */
    private String language;

    /**
     * 民族
     */
    private String nation;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 婚姻（1未婚 2已婚 3其他）
     */
    private String marital;

    /**
     * 身份证号码
     */
    private String idCardNumber;

    /**
     * 是否认证（Y是 N否）
     */
    private String isAuth;

    /**
     * 云账户
     */
    private String cloudAccount;

    /**
     * 生日日期
     */
    private LocalDate birthdayDate;

    /**
     * 农历生日
     */
    private String chineseBirthdayDate;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 性别（0男 1女）
     */
    private String sex;

    /**
     * 升高
     */
    private Double height;

    /**
     * 体重
     */
    private Double weight;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 户籍所在地
     */
    private String registeredResidence;

    /**
     * 现住地址
     */
    private String address;

    /**
     * 健康状态（1良好 2一般 3其他）
     */
    private String healthStatus;

    /**
     * 是否有传染病（Y是 N否）
     */
    private String isInfectiousDiseases;

    /**
     * 紧急联系人姓名
     */
    private String emergencyContactName;

    /**
     * 紧急联系人类型
     */
    private String emergencyContactType;

    /**
     * 紧急联系人电话
     */
    private String emergencyContactPhone;

    /**
     * 是否有亲属在公司（Y是 N否）
     */
    private String isRelativesHere;

    /**
     * 亲属姓名
     */
    private String relativesName;

    /**
     * 亲属关系
     */
    private String relativesRelationship;

    /**
     * 合作时间
     */
    private LocalDate cooperationDate;

    /**
     * 学历扩展信息：
     * enrollment_date 入学日期
     * graduation_date 毕业日期
     * school 学校
     * major 专业
     * education 学历
     * degree 学位
     */
    private String educationExtra;

    /**
     * 工作扩展信息：
     * employment_date 入职日期
     * resignation_date 离职日期
     * company 公司
     * position 职位
     * salary 薪资
     * certifier 证明人
     * certifier_phone 证明人电话
     */
    private String workExtra;

    /**
     * 家庭扩展信息：
     * member_name 成员姓名
     * relationship 关系
     * contact_phone 联系电话
     * work 工作
     * remark 备注
     */
    private String familyExtra;

    /**
     * 直播状态 (0正常、1未开、2休播、3自离、4淘汰 5待停播)
     */
    private Integer liveStatus;

    /**
     * 特长
     */
    private String talent;

    /**
     * 是否有过直播经验（Y是 N否）
     */
    private String isLive;

    /**
     * 直播平台及时长
     */
    private String livePlatform;

    /**
     * 期望薪资
     */
    private BigDecimal expectationSalary;

    /**
     * 运营id
     */
    private Long operateId;

    /**
     * 是否内推
     */
    private String isRecommend;

    /**
     * 应聘岗位
     */
    private String position;

    /**
     * 是否住宿（Y是 N否）
     */
    private String isLodging;

    /**
     * 学历类型
     */
    private String educationType;

    /**
     * 学位类型
     */
    private String degreeType;

    /**
     * 专业
     */
    private String major;

    /**
     * 是否有竞业协议（Y是 N否）
     */
    private String isNca;

    /**
     * 停播时间
     */
    private LocalDate stopLiveDate;

    /**
     * 开播日期
     */
    private LocalDate startLiveDate;

    /**
     * 登记表URL
     */
    private String registrationFormUrl;
}