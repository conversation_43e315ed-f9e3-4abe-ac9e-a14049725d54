package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import com.yooa.common.core.annotation.ExcelDict;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/5/12 15:24
 * @Description:
 */
@Data
public class CrmCommonFriendVo {

    /**
     * 客户名
     */
    @Excel(name = "客户名")
    private String name;

    /**
     * 好友id
     */
    private Long friendId;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 性别(字典)
     */
    @Excel(name = "性别")
    @ExcelDict(dictType = "common_sex_type")
    private String sex;

    /**
     * 年龄
     */
    @Excel(name = "年龄")
    private String age;

    /**
     * 地区
     */
    @Excel(name = "地区")
    @ExcelDict(dictType = "crm_friend_area_type")
    private String area;

    /**
     * 语种(字典)
     */
    @Excel(name = "语种")
    @ExcelDict(dictType = "sys_friend_language_type")
    private String language;

    /**
     * 薪资
     */
    @Excel(name = "薪资")
    private String incomeLevel;


    /**
     * 工作类型(字典)
     */
    @Excel(name = "工作类型")
    @ExcelDict(dictType = "crm_friend_work_type")
    private String workType;

    /**
     * 情感需求(字典)
     */
    @Excel(name = "情感需求")
    @ExcelDict(dictType = "crm_friend_emotion_type")
    private String demand;

    /**
     * 停止时间
     */
    @Excel(name = "停止时间")
    private LocalDateTime loseTime;

    /**
     * 粉丝达成最高分
     */
    @Excel(name = "粉丝达成最高分")
    @ExcelDict(dictType = "fans_type")
    private String maxFansType;

    /**
     * 录入日期
     */
    @Excel(name = "录入日期")
    private LocalDate recordTime;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
