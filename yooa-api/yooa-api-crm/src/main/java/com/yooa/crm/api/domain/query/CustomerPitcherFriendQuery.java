package com.yooa.crm.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;


@Data
public class CustomerPitcherFriendQuery extends QueryEntity {

    // 用户ID
    @NotNull(message = "缺少用户ID!")
    private Long userId;

    // 用户类型(1:推广、2:运营、7:vip、8:投放(部门类型))
    @NotNull(message = "缺少用户类型!")
    private Long userType;

    // 权限范围(0:查询自己、1:查询包含下级)
    @NotNull(message = "请查询范围!")
    private Long dataScope;

    // 查询类型(0:全部、1:推广、2:VIP、3:运营)
    private List<Integer> dataScopeType;

    // 部门ID
    private Long deptId;
    // 投放 - 推广部门ID
    private Long extendDeptId;

    // 搜索类型(0:客户(客户ID、好友名)、1:推广(推广负责人、渠道负责人)、2:客服(客服负责人)、3:运营(运营负责人、主播名))
    private Long searchType;

    // 搜索框内容(searchType)
    private String searchBox;

    // 主渠道(字典)
    private Long mainChannelId;

    // 子渠道(字典)
    private Long subChannelId;

    // 渠道负责人
    private Long channelUserId;

    // 领取人
    private Long receiveId;

    // 地区
    private Long area;

    // 语言(字典)
    private Long language;

    // 粉丝类型（1男粉 2女粉 3英文粉 4中文粉）
    private Long fansType;

    // 职业
    private Long workType;

    // 情感
    private Long demand;

    // 性别(字典)
    private Long sex;

    // 状态(0:好友、1:注册、2:一交、3:二交)
    private Long status;

    // 优质类型(0:优质用户、1:普通用户)
    private Long qualityType;

    // 绑定状态(0:在绑、1:解绑)
    private Long bindStatus;

    // 类型(0:无充值、1:首充、2:200粉、3:5k粉、4:5w粉)
    private Long type;

    // 大小号(0:大号、1:小号)
    private Integer accountType;

    /**
     * 查询开始金额
     */
    private Long beginMoney;

    /**
     * 查询结束金额
     */
    private Long endMoney;

    /**
     * 查询开始总金额
     */
    private Long beginTotalMoney;

    /**
     * 查询结束总金额
     */
    private Long endTotalMoney;

    /**
     * 开始年龄
     */
    private Long beginAge;

    /**
     * 结束年龄
     */
    private Long endAge;

    /**
     * 修改开始时间
     */
    private LocalDateTime updateBeginTime;

    /**
     * 修改结束时间
     */
    private LocalDateTime updateEndTime;

    /**
     * 注册开始时间
     */
    private LocalDateTime createBeginTime;

    /**
     * 注册结束时间
     */
    private LocalDateTime createEndTime;

    /**
     * 充值筛选开始时间
     */
    private LocalDateTime rechargeBeginTime;

    /**
     * 充值筛选结束时间
     */
    private LocalDateTime rechargeEndTime;

    /**
     * 粉丝达成筛选开始时间
     */
    private LocalDateTime fansBeginTime;

    /**
     * 粉丝达成筛选结束时间
     */
    private LocalDateTime fansEndTime;

    /**
     * 录入开始时间
     */
    private LocalDateTime recordBeginTime;

    /**
     * 录入结束时间
     */
    private LocalDateTime recordEndTime;

    /**
     * 最后登入开始时间
     */
    private LocalDateTime lastLoginBeginTime;

    /**
     * 最后登入结束时间
     */
    private LocalDateTime lastLoginEndTime;

    /**
     * 二交开始时间
     */
    private LocalDateTime serveBeginTime;

    /**
     * 二交结束时间
     */
    private LocalDateTime serveEndTime;

    /**
     * 优质开始时间
     */
    private LocalDateTime qualityBeginTime;

    /**
     * 优质结束时间
     */
    private LocalDateTime qualityEndTime;

    /***二次查询的条件***/

    /**
     * 好友ID集
     */
    private List<Long> friendIds;

    /**
     * 领取人ID集
     */
    private List<Long> receiveIds;

    /**
     * 客户ID集
     */
    private List<Long> customerIds;

    /**
     * true:分页、false:不分页
     */
    private boolean sizeBl;
}
