package com.yooa.crm.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/4/18 17:00
 * @Description:
 */
@Data
public class CustomerJoinServeQuery extends QueryEntity {

    /**
     * 查询类型 0: 推广二交 1:VIP二交
     */
    private Integer type;

    /**
     * 大小号 0:大号 1:小号
     */
    private Integer accountType;

    /**
     * 开始时间
     */
    private LocalDateTime receiveBeginTime;

    /**
     * 结束时间
     */
    private LocalDateTime receiveEndTime;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 客户名称或ID查询
     */
    private String customerNameOrIdQuery;

    /**
     * 语种
     */
    private Integer language;

    /**
     * 粉丝类型（1男粉 2女粉 3英文粉 4中文粉）
     */
    private Integer fansType;

    /**
     * 绑定类型 0:未绑定好友 1:已绑定好友
     */
    private String bindType;

    /**
     * 模块类型 （extend：推广，pitcher：投放）
     */
    private String moduleType;
}
