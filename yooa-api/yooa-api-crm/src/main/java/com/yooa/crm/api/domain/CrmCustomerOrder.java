package com.yooa.crm.api.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.core.annotation.Excel;
import lombok.Data;
import org.apache.ibatis.annotations.Options;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户充值订单表
 */
@Data
public class CrmCustomerOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @TableId(value = "order_id")
    @Excel(name = "订单ID")
    private Long orderId;

    /**
     * 订单编号
     */
    @Excel(name = "订单编号")
    private String orderNo;

    /**
     * 订单时间
     */
    @Excel(name = "订单时间")
    private LocalDateTime orderTime;

    /**
     * 订单完成时间
     */
    @Excel(name = "订单完成时间")
    private LocalDateTime completeTime;

    /**
     * 订单金额
     */
    @Excel(name = "订单金额")
    private BigDecimal orderMoney;

    /**
     * 订单状态(1:已完成、2:已退款、3:特殊退款)
     */
    private Integer orderStatus;

    /**
     * 特殊退款时间
     */
    private LocalDateTime orderRefundTime;

    /**
     * 支付方式
     */
    @Excel(name = "支付方式", readConverterExp = "2=Visa-2,9=手动充值,10=谷歌支付,11=苹果支付,12=Payermax,13=MyCard,14=croPay,15=wechatHK,16=paypal支付,17=富責東支付,18=uniPay支付,19=官网paypal支付,20=coinPay支付,21=代购申请,22=空中云汇,23=华为内购,24=三星内购,25=FunPay,26=蚂蚁国际")
    private Integer paymentType;

    /**
     * pd订单号
     */
    private String pdOrderNo;

    /**
     * pd第三方订单号
     */
    @Excel(name = "pd第三方订单号")
    private String pdThirdOrderNo;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * py推广id
     */
    private Long pyExtendId;

    /**
     * py客服id
     */
    private Long pyServeId;

    /**
     * 推广ID
     */
    private Long extendId;

    /**
     * 推广部门id
     */
    private Long extendDeptId;

    /**
     * 客服id
     */
    private Long serveId;

    /**
     * 客服部门id
     */
    private Long serveDeptId;

    /**
     * 投手id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long pitcherId;

    /**
     * 投手部门id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long pitcherDeptId;

    /**
     * 平台 1=pd,2=潘多拉,3=1v1,4=glowfun,5=poyo'
     */
    private Long appProject;

}