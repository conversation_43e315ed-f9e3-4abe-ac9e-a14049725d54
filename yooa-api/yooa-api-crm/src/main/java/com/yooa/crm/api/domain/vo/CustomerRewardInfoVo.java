package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import com.yooa.common.core.annotation.ExcelDict;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/21 9:44
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerRewardInfoVo extends AnchorRewardInfoVo {

    /**
     * 打赏时间
     */
    @Excel(name = "打赏时间")

   private LocalDateTime rewardTime;

    /**
     * 主播网名
     */
    @Excel(name = "主播网名")
   private String anchorNickName;

    /**
     * 打赏金额
     */
    @Excel(name = "打赏金额")

   private BigDecimal rewardAmt;

    /**
     * 收支行为 (字典)
     */

    @ExcelDict(dictType = "crm_anchor_action")
    @Excel(name = "收支行为")
 //   @Excel(name = "收支行为",readConverterExp = "sendgift=赠送礼物,sendgift=赠送礼物,sendbarrage=弹幕,loginbonus=登录奖励,buyvip=购买VIP,buycar=购买坐骑,buyliang=购买靓号,sendred=发送红包,robred=抢红包,buyguard=开通守护,reg_reward=注册奖励,luckgift=礼物中奖,jackpotwin=奖池中奖,uname=修改名字,upvideo=上传视频,videolikes=上传视频点赞,buynoble=开通贵族,buyavatar=购买头像框,community=社区动态送礼,draw=抽奖,prop=购买道具,turntable=整蛊转盘,blind_box=盲盒,red_packet=人氣紅包,fans_exclusive_pack=粉絲團禮包,fans_pack_send=粉絲團包裹贈送禮物" )
   private String action;

    /**
     * 礼物
     */
    @Excel(name = "礼物")
   private String giftName;

    private Long id;
}
