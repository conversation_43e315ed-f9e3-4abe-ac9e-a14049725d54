package com.yooa.crm.api.domain.vo;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/4/29 下午1:33
 */

import com.yooa.common.core.annotation.Excel;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 自由导出类
 */
@Data
public class FreeExportVo {

    /**
     * 好友id
     */
    @Excel(name = "好友id")
    private Long friendId;

    /**
     * 好友名
     */
    @Excel(name = "好友名")
    private String friendName;

    /**
     * 好友领取人ID
     */
    @Excel(name = "好友领取人ID")
    private Long friendReceiveId;

    /**
     * 好友领取人
     */
    @Excel(name = "好友领取人")
    private String friendReceiveNickName;

    /**
     * 好友领取人部门
     */
    @Excel(name = "好友领取人部门")
    private String friendReceiveDeptName;

    /**
     * 联系方式
     */
    @Excel(name = "联系方式")
    private String contactMode;

    /**
     * 联系号码
     */
    @Excel(name = "联系号码")
    private String contactPhone;

    /**
     * 记录时间
     */
    @Excel(name = "记录时间")
    private LocalDate recordTime;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间")
    private LocalDateTime createTime;

    /**
     * 主渠道
     */
    @Excel(name = "主渠道")
    private String mainChannelName;

    /**
     * 子渠道
     */
    @Excel(name = "子渠道")
    private String subChannelName;

    /**
     * 渠道用户ID
     */
    @Excel(name = "渠道用户ID")
    private Long channelUserId;

    /**
     * 渠道用户名
     */
    @Excel(name = "渠道用户名")
    private String channelUserNickName;

    /**
     * 好友创建人ID
     */
    @Excel(name = "好友创建人ID")
    private Long createId;

    /**
     * 好友创建人名
     */
    @Excel(name = "好友创建人名")
    private String createNickName;

    /**
     * 收入水平
     */
    @Excel(name = "收入水平")
    private String incomeLevel;

    /**
     * 预计消费
     */
    @Excel(name = "预计消费")
    private String projectedConsumption;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 领取次数
     */
    @Excel(name = "领取次数")
    private Integer receiveNumber;

    /**
     * 领取时间
     */
    @Excel(name = "领取时间")
    private LocalDateTime receiveTime;

    /**
     * 流失时间
     */
    @Excel(name = "流失时间")
    private LocalDateTime loseTime;

    /**
     * 最后登入时间
     */
    @Excel(name = "最后登入时间")
    private LocalDateTime lastLoginTime;

    /**
     * 年龄
     */
    @Excel(name = "年龄")
    private Long age;

    /**
     * 性别
     */
    @Excel(name = "性别")
    private String sex;

    /**
     * 语种
     */
    @Excel(name = "语种")
    private String language;

    /**
     * 粉丝类型
     */
    @Excel(name = "粉丝类型")
    private String fansType;

    /**
     * 地区
     */
    @Excel(name = "地区")
    private String area;

    /**
     * 工种
     */
    @Excel(name = "工种")
    private String workType;

    /**
     * 婚姻状态
     */
    @Excel(name = "婚姻状态")
    private String matrimony;

    /**
     * 情感需求
     */
    @Excel(name = "情感需求")
    private String demand;

    /**
     * 质量
     */
    @Excel(name = "质量")
    private String quality;

    /**
     * 绑定中的客户集ID
     */
    @Excel(name = "绑定中的客户集ID")
    private String bindCustomerId;

    /**
     * 绑定中的客户集名
     */
    @Excel(name = "绑定中的客户集名")
    private String bindCustomerName;

    /**
     * 解绑的客户集ID
     */
    @Excel(name = "解绑的客户集ID")
    private String unbindCustomerId;

    /**
     * 解绑的客户集名
     */
    @Excel(name = "解绑的客户集名")
    private String unbindCustomerName;
    /**
     * 素材类型
     */
    @Excel(name = "素材类型")
    private String materialTypeName;

    /**
     * 作息时间
     */
    @Excel(name = "作息时间")
    private String restTime;

    /**
     * 娱乐方式
     */
    @Excel(name = "娱乐方式")
    private String playWay;

    /**
     * 感兴趣话题
     */
    @Excel(name = "感兴趣话题")
    private String concernTownTalk;

    /**
     * 敏感话题
     */
    @Excel(name = "敏感话题")
    private String sensitiveTownTalk;

    /**
     * 一级地区
     */
    @Excel(name = "一级地区")
    private String mainAreaName;
    /**
     * 二级地区
     */
    @Excel(name = "二级地区")
    private String subAreaName;


}
