package com.yooa.crm.api.domain;


import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户与好友绑定关系表
 */
@Data
public class CrmCustomerFriend extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 好友id
     */
    private Long friendId;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * py推广id
     */
    private Long pyExtendId;

    /**
     * 现阶段状态(0:绑定中、1:已解绑)
     */
    private Integer status;

    /**
     * 开始时间（更新时间起始）
     */
    private LocalDateTime beginTime;

    /**
     * 结束时间（更新时间结束）
     */
    private LocalDateTime endTime;

    /**
     * 优质达成时间
     */
    private LocalDateTime qualityTime;

}