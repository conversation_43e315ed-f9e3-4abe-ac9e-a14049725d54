package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/19 15:21
 * @Description:
 */
@Data
public class AnchorRewardInfoVo {

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 主播账号id
     */
    private Long anchorAccountId;

    /**
     * 客户id
     */
    @Excel(name = "客户id")
    private Long customerId;

    /**
     * 客户名
     */
    @Excel(name = "客户名")
    private String customerName;

    /**
     * 主播名
     */
    private String anchorName;

    /**
     * 打赏时间
     */
    private LocalDateTime rewardTime;

    /**
     * 打赏金额
     */
    private BigDecimal rewardMoney;

    /**
     * 好友id
     */
    private Long friendId;

    /**
     * 收支行为 字典
     * "sendgift"=>"赠送礼物",
     * "sendbarrage"=>"弹幕",
     * "loginbonus"=>"登录奖励",
     * "buyvip"=>"购买VIP",
     * "buycar"=>"购买坐骑",
     * "buyliang"=>"购买靓号",
     * 'sendred'=>'发送红包',
     * 'robred'=>'抢红包',
     * 'buyguard'=>'开通守护',
     * 'reg_reward'=>'注册奖励',
     * 'luckgift'=>'礼物中奖',
     * 'jackpotwin'=>'奖池中奖',
     * 'uname'=>'修改名字',
     * 'upvideo'=>'上传视频',
     * 'videolikes'=>'上传视频点赞',
     * 'buynoble'=>'开通贵族'，
     * 'buyavatar'=>'购买头像框',
     * 'community'=>'社区动态送礼',
     * 'draw'=>'抽奖',
     * 'prop'=>'购买道具',
     * 'turntable' => '整蛊转盘',
     * 'blind_box' => '盲盒',
     * 'red_packet'=>'人氣紅包',
     * 'fans_exclusive_pack'=>'粉絲團禮包',
     * 'fans_pack_send'=>'粉絲團包裹贈送禮物',
     */
    private String action;

    /**
     * 礼物名称
     */
    private String giftName;
}
