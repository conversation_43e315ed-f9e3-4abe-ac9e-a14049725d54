package com.yooa.crm.api.domain.query;

import com.yooa.common.core.annotation.Excel;
import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> xh
 * @Date: 2025/4/11 9:55
 * @Description:
 */
@Data
public class AnchorQuery extends QueryEntity {

    /**
     *  部门ID
     */
    private Long deptId;

    /**
     * 语言（1中文 2英文）
     */
    private List<String> language;

    /**
     * 地区
     */
    private List<String> region;

    /**
     * 风格
     */
    private List<String> anchorStyle;

    /**
     * 主播类型（1线上 2线下 3兼职）
     */
    private List<String> anchorType;

    /**
     * 主播状态 (0正常、1未开、2休播、3自离、4淘汰 5待停播)
     */
    private List<String> status;

    /**
     * 开播时间(开始)
     */
    private LocalDateTime liveStartTime;

    /**
     * 开播时间(结束)
     */
    private LocalDateTime liveEndTime;

    /**
     * 根据pd账号或姓名或花名搜索
     */
    private String anchorIdOrNameQuery;

    /**
     * 性别
     */
    private List<String> sex;

    /**
     * firstCharge=首充 fans1h=100粉  fans2h=200粉 fans5k=500粉 fans5k=5K粉 fans10w=10W粉 firstChargeRate=首充转换率 joinFans=交接粉丝 joinFansRate=交接粉丝打赏率 liveHours=直播时长 reward=交接打赏 totalReward=总打赏
     */
    private String filed;

    /**
     * DESC 降序 ASC 升序
     */
    private String order;

}
