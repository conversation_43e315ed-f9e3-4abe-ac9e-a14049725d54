package com.yooa.crm.api.domain.vo;

import com.yooa.crm.api.domain.CrmFriend;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
public class FriendVo extends CrmFriend implements Serializable {

    /**
     * 一级渠道名称
     */
    private String mainChannelName;
    /**
     * 二级渠道名称
     */
    private String subChannelName;
    /**
     * 渠道负责人/投手 名称
     */
    private String channelUserName;
    /**
     * 渠道负责人/投手 昵称
     */
    private String channelNickName;
    /**
     * 领取人信息(现维护团队信息)
     */
    private ExtendVo receiveMessage;
    /**
     * 创建人信息(录入团队信息)
     */
    private ExtendVo createMessage;

    /**
     * 投手部门ID
     */
    private Long pitcherDeptId;
}
