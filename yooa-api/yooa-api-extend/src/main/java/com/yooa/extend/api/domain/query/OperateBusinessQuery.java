package com.yooa.extend.api.domain.query;

import com.yooa.common.core.annotation.Excel;
import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/7/12 14:22
 * @Description:
 */
@Data
public class OperateBusinessQuery extends QueryEntity {

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 员工状态(字典)
     */
    private String status;

    /**
     * 搜索条件开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型(type:5员工、4主管、3经理、2总监、1总经理(除去员工查个人，其他查部门))
     */
    @NotNull(message = "请选择类型")
    private Integer type;

    /**
     * 部门级别 （1集团 2公司 3部门 4团队 5小组）
     */
    private Integer deptLevel;

    /**
     * beginToShowNum=开播数 offAirBroadcastNum=休播数  discontinueBroadcastingNum=停播数 toBeDiscontinued=待停播 handoverNum=交接数 totalRewardNum=总打赏数 handoverRewardNum=接粉打赏数
     * handoverRewardRate=接粉打赏率 firstChargeNum=首充粉 handoverFirstChargeRate=接粉手冲率 fans2h=200粉 fans5k=5k粉 fans5w=5w粉 fans10w=10w粉 rewardAmount=打赏业绩
     */
    private String filed;

    /**
     * DESC 降序 ASC 升序
     */
    private String order;

}
